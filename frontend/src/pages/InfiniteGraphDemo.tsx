import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import InfiniteKnowledgeGraph from '@/components/organisms/InfiniteKnowledgeGraph';
import { GraphData, GraphNode, PerformanceMetrics } from '@/types/graph';
import {
  Sparkles,
  Brain,
  Network,
  Zap,
  Target,
  TrendingUp,
  Activity,
  Database,
  Clock,
  Users,
  Globe,
  Cpu,
  MemoryStick,
  Gauge
} from 'lucide-react';

// Sample data for demonstration
const sampleGraphData: GraphData = {
  nodes: [
    {
      id: 'vitamin-d3',
      labels: ['SUPPLEMENT'],
      properties: {
        name: 'Vitamin D3',
        description: 'Essential vitamin for bone health and immune function',
        category: 'SUPPLEMENT',
        confidence: 0.95,
        importance: 0.9,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'calcium',
      labels: ['INGREDIENT'],
      properties: {
        name: 'Calcium',
        description: 'Essential mineral for bone and teeth health',
        category: 'INGREDIENT',
        confidence: 0.98,
        importance: 0.85,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'bone-health',
      labels: ['CONDITION'],
      properties: {
        name: 'Bone Health',
        description: 'Overall health and strength of bones',
        category: 'CONDITION',
        confidence: 0.92,
        importance: 0.8,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'immune-system',
      labels: ['CONDITION'],
      properties: {
        name: 'Immune System',
        description: 'Body\'s defense mechanism against diseases',
        category: 'CONDITION',
        confidence: 0.94,
        importance: 0.88,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'magnesium',
      labels: ['INGREDIENT'],
      properties: {
        name: 'Magnesium',
        description: 'Essential mineral for muscle and nerve function',
        category: 'INGREDIENT',
        confidence: 0.96,
        importance: 0.82,
        createdAt: new Date().toISOString()
      }
    }
  ],
  relationships: [
    {
      id: 'rel-1',
      type: 'TREATS',
      source: 'vitamin-d3',
      target: 'bone-health',
      properties: {
        strength: 'high',
        confidence: 0.9,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'rel-2',
      type: 'ENHANCES',
      source: 'vitamin-d3',
      target: 'immune-system',
      properties: {
        strength: 'medium',
        confidence: 0.85,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'rel-3',
      type: 'SYNERGISTIC_WITH',
      source: 'vitamin-d3',
      target: 'calcium',
      properties: {
        strength: 'high',
        confidence: 0.92,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'rel-4',
      type: 'TREATS',
      source: 'calcium',
      target: 'bone-health',
      properties: {
        strength: 'high',
        confidence: 0.95,
        createdAt: new Date().toISOString()
      }
    },
    {
      id: 'rel-5',
      type: 'SYNERGISTIC_WITH',
      source: 'calcium',
      target: 'magnesium',
      properties: {
        strength: 'medium',
        confidence: 0.88,
        createdAt: new Date().toISOString()
      }
    }
  ]
};

const InfiniteGraphDemo: React.FC = () => {
  const [graphData, setGraphData] = useState<GraphData>(sampleGraphData);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    updateTime: 0,
    memoryUsage: 0,
    fps: 60,
    nodeCount: 0,
    relationshipCount: 0,
    visibleNodes: 0,
    visibleRelationships: 0
  });
  const [demoStats, setDemoStats] = useState({
    totalExpansions: 0,
    aiProcessingCount: 0,
    crawledPages: 0,
    realTimeUpdates: 0,
    connectedUsers: 1
  });

  // Simulate real-time updates for demo
  useEffect(() => {
    const interval = setInterval(() => {
      setDemoStats(prev => ({
        ...prev,
        realTimeUpdates: prev.realTimeUpdates + Math.floor(Math.random() * 3),
        connectedUsers: 1 + Math.floor(Math.random() * 5)
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Handle node selection
  const handleNodeSelect = useCallback((node: GraphNode) => {
    setSelectedNode(node);
    setDemoStats(prev => ({ ...prev, totalExpansions: prev.totalExpansions + 1 }));
  }, []);

  // Handle graph updates
  const handleGraphUpdate = useCallback((newData: GraphData) => {
    setGraphData(newData);
    setPerformanceMetrics(prev => ({
      ...prev,
      nodeCount: newData.nodes.length,
      relationshipCount: newData.relationships.length,
      visibleNodes: newData.nodes.length,
      visibleRelationships: newData.relationships.length
    }));
  }, []);

  // Demo actions
  const simulateAIProcessing = () => {
    setDemoStats(prev => ({ ...prev, aiProcessingCount: prev.aiProcessingCount + 1 }));
    
    // Add a new node after "AI processing"
    setTimeout(() => {
      const newNode: GraphNode = {
        id: `ai-generated-${Date.now()}`,
        labels: ['STUDY'],
        properties: {
          name: 'AI Generated Study',
          description: 'Study discovered through AI processing',
          category: 'STUDY',
          confidence: 0.87,
          importance: 0.75,
          createdAt: new Date().toISOString()
        }
      };

      const newRelationship = {
        id: `ai-rel-${Date.now()}`,
        type: 'SUPPORTS',
        source: newNode.id,
        target: 'vitamin-d3',
        properties: {
          strength: 'medium' as const,
          confidence: 0.82,
          createdAt: new Date().toISOString()
        }
      };

      setGraphData(prev => ({
        nodes: [...prev.nodes, newNode],
        relationships: [...prev.relationships, newRelationship]
      }));
    }, 2000);
  };

  const simulateCrawling = () => {
    setDemoStats(prev => ({ ...prev, crawledPages: prev.crawledPages + 1 }));
    
    // Add multiple nodes after "crawling"
    setTimeout(() => {
      const crawledNodes: GraphNode[] = [
        {
          id: `crawled-ingredient-${Date.now()}`,
          labels: ['INGREDIENT'],
          properties: {
            name: 'Vitamin K2',
            description: 'Fat-soluble vitamin important for bone metabolism',
            category: 'INGREDIENT',
            confidence: 0.91,
            importance: 0.78,
            createdAt: new Date().toISOString()
          }
        },
        {
          id: `crawled-mechanism-${Date.now()}`,
          labels: ['MECHANISM'],
          properties: {
            name: 'Calcium Absorption',
            description: 'Process by which calcium is absorbed in the intestines',
            category: 'MECHANISM',
            confidence: 0.89,
            importance: 0.72,
            createdAt: new Date().toISOString()
          }
        }
      ];

      const crawledRelationships = [
        {
          id: `crawled-rel-1-${Date.now()}`,
          type: 'ACTS_VIA',
          source: 'vitamin-d3',
          target: crawledNodes[1].id,
          properties: {
            strength: 'high' as const,
            confidence: 0.88,
            createdAt: new Date().toISOString()
          }
        },
        {
          id: `crawled-rel-2-${Date.now()}`,
          type: 'SYNERGISTIC_WITH',
          source: crawledNodes[0].id,
          target: 'vitamin-d3',
          properties: {
            strength: 'medium' as const,
            confidence: 0.85,
            createdAt: new Date().toISOString()
          }
        }
      ];

      setGraphData(prev => ({
        nodes: [...prev.nodes, ...crawledNodes],
        relationships: [...prev.relationships, ...crawledRelationships]
      }));
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 flex items-center justify-center gap-3">
            <Sparkles className="text-blue-500" />
            Infinite Knowledge Graph
            <Sparkles className="text-blue-500" />
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the future of supplement knowledge discovery with our AI-powered, 
            infinitely expanding graph that learns and grows in real-time.
          </p>
        </div>

        {/* Stats Dashboard */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Target className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{demoStats.totalExpansions}</div>
              <div className="text-sm text-gray-600">Graph Expansions</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Brain className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{demoStats.aiProcessingCount}</div>
              <div className="text-sm text-gray-600">AI Processes</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Globe className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{demoStats.crawledPages}</div>
              <div className="text-sm text-gray-600">Pages Crawled</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Activity className="w-8 h-8 text-orange-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{demoStats.realTimeUpdates}</div>
              <div className="text-sm text-gray-600">Live Updates</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Users className="w-8 h-8 text-red-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{demoStats.connectedUsers}</div>
              <div className="text-sm text-gray-600">Connected Users</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Graph Visualization */}
          <div className="lg:col-span-3">
            <Card className="h-[800px]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="w-5 h-5" />
                  Interactive Knowledge Graph
                  <Badge variant={isConnected ? "default" : "destructive"} className="ml-auto">
                    {isConnected ? "🟢 Live" : "🔴 Demo Mode"}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0 h-full">
                <InfiniteKnowledgeGraph
                  initialData={graphData}
                  width={1000}
                  height={700}
                  autoExpansion={true}
                  expansionThreshold={0.7}
                  maxNodes={1000}
                  onNodeSelect={handleNodeSelect}
                  onGraphUpdate={handleGraphUpdate}
                  className="h-full"
                />
              </CardContent>
            </Card>
          </div>

          {/* Side Panel */}
          <div className="space-y-6">
            {/* Demo Controls */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Demo Controls</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={simulateAIProcessing}
                  className="w-full"
                  variant="default"
                >
                  <Brain className="w-4 h-4 mr-2" />
                  Simulate AI Processing
                </Button>
                
                <Button 
                  onClick={simulateCrawling}
                  className="w-full"
                  variant="outline"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  Simulate Web Crawling
                </Button>
                
                <Alert>
                  <Zap className="h-4 w-4" />
                  <AlertDescription>
                    Click nodes to expand the graph or use the demo buttons to see AI-powered growth!
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Gauge className="w-5 h-5" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>FPS</span>
                    <span>{performanceMetrics.fps}</span>
                  </div>
                  <Progress value={(performanceMetrics.fps / 60) * 100} />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Render Time</span>
                    <span>{performanceMetrics.renderTime.toFixed(1)}ms</span>
                  </div>
                  <Progress value={Math.min((performanceMetrics.renderTime / 100) * 100, 100)} />
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <Database className="w-4 h-4 mx-auto mb-1" />
                    <div className="font-bold">{performanceMetrics.nodeCount}</div>
                    <div>Nodes</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <Network className="w-4 h-4 mx-auto mb-1" />
                    <div className="font-bold">{performanceMetrics.relationshipCount}</div>
                    <div>Edges</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Selected Node Info */}
            {selectedNode && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Selected Node</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <h3 className="font-bold text-lg">{selectedNode.properties?.name}</h3>
                    <p className="text-sm text-gray-600">
                      {selectedNode.properties?.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">
                        {selectedNode.labels?.[0]}
                      </Badge>
                      <Badge variant="secondary">
                        {(selectedNode.properties?.confidence * 100).toFixed(0)}% confidence
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-500">
                      <Clock className="w-3 h-3 inline mr-1" />
                      {new Date(selectedNode.properties?.createdAt).toLocaleString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InfiniteGraphDemo;
