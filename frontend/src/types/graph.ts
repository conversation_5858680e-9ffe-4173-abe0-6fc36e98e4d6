// Core graph data structures
export interface GraphNode {
  id: string;
  labels?: string[];
  properties: {
    name?: string;
    description?: string;
    category?: string;
    confidence?: number;
    importance?: number;
    createdAt?: string;
    updatedAt?: string;
    [key: string]: any;
  };
}

export interface GraphRelationship {
  id: string;
  type: string;
  source: string;
  target: string;
  properties: {
    strength?: 'low' | 'medium' | 'high';
    confidence?: number;
    weight?: number;
    createdAt?: string;
    updatedAt?: string;
    [key: string]: any;
  };
}

export interface GraphData {
  nodes: GraphNode[];
  relationships: GraphRelationship[];
}

// Visualization state
export interface GraphViewState {
  zoom: number;
  pan: { x: number; y: number };
  selectedNodes: Set<string>;
  highlightedNodes: Set<string>;
  showLabels: boolean;
  showRelationshipLabels: boolean;
  nodeSize: number;
  linkDistance: number;
  chargeStrength: number;
  layoutMode: 'force' | 'hierarchical' | 'circular' | 'grid';
  animationSpeed: number;
  particleEffects: boolean;
  hoverEffects: boolean;
  clustering: {
    enabled: boolean;
    threshold: number;
    algorithm: 'modularity' | 'louvain' | 'leiden' | 'hierarchical';
  };
}

// Clustering data
export interface ClusterData {
  id: string;
  name: string;
  nodes: string[];
  center: { x: number; y: number };
  color: string;
  size: number;
  density: number;
  modularity: number;
}

// Expansion options
export interface ExpansionOptions {
  nodeId: string;
  expansionType: 'related' | 'similar' | 'interactions' | 'studies' | 'mechanisms';
  limit: number;
  depth: number;
  filters?: {
    nodeTypes?: string[];
    relationshipTypes?: string[];
    confidenceThreshold?: number;
  };
}

// Graph update events
export interface GraphUpdateEvent {
  type: 'node_created' | 'node_updated' | 'node_deleted' | 
        'relationship_created' | 'relationship_updated' | 'relationship_deleted' | 
        'graph_expanded' | 'clustering_updated' | 'inference_completed';
  data: any;
  timestamp: string;
  source: 'manual' | 'crawl' | 'ai_processing' | 'auto_inference' | 'websocket';
  confidence?: number;
  metadata?: {
    sourceUrl?: string;
    processingTime?: number;
    algorithm?: string;
    [key: string]: any;
  };
}

// Animation configurations
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bounce';
  delay?: number;
  repeat?: number;
  yoyo?: boolean;
}

export interface NodeAnimation {
  nodeId: string;
  type: 'scale' | 'color' | 'position' | 'opacity' | 'pulse' | 'glow';
  config: AnimationConfig;
  properties: {
    from: any;
    to: any;
  };
}

export interface ParticleEffect {
  id: string;
  type: 'sparkle' | 'explosion' | 'wave' | 'trail' | 'pulse';
  position: { x: number; y: number };
  config: {
    count: number;
    speed: number;
    size: number;
    color: string;
    lifetime: number;
  };
}

// Interaction feedback
export interface InteractionFeedback {
  visual: {
    highlight: boolean;
    glow: boolean;
    scale: number;
    opacity: number;
  };
  audio: {
    enabled: boolean;
    sound: 'hover' | 'click' | 'expand' | 'error' | 'success';
    volume: number;
  };
  haptic: {
    enabled: boolean;
    pattern: 'light' | 'medium' | 'heavy' | 'double' | 'triple';
  };
}

// Performance metrics
export interface PerformanceMetrics {
  renderTime: number;
  updateTime: number;
  memoryUsage: number;
  fps: number;
  nodeCount: number;
  relationshipCount: number;
  visibleNodes: number;
  visibleRelationships: number;
}

// Search and filtering
export interface GraphFilters {
  nodeTypes: string[];
  relationshipTypes: string[];
  confidenceThreshold: number;
  strengthLevels: string[];
  dateRange?: [Date, Date];
  searchQuery: string;
  customFilters: { [key: string]: any };
}

export interface SearchResult {
  node: GraphNode;
  score: number;
  matches: {
    field: string;
    value: string;
    highlight: string;
  }[];
}

// Layout algorithms
export interface LayoutConfig {
  algorithm: 'force' | 'hierarchical' | 'circular' | 'grid' | 'radial' | 'tree';
  parameters: {
    [key: string]: number | string | boolean;
  };
  constraints?: {
    fixedNodes?: string[];
    boundaries?: { x: [number, number]; y: [number, number] };
    grouping?: { [groupId: string]: string[] };
  };
}

// Data extraction and processing
export interface ExtractedEntity {
  id: string;
  type: string;
  name: string;
  description?: string;
  confidence: number;
  properties: { [key: string]: any };
  relationships: {
    target: string;
    type: string;
    confidence: number;
    properties?: { [key: string]: any };
  }[];
}

export interface ExtractionResult {
  entities: ExtractedEntity[];
  relationships: {
    source: string;
    target: string;
    type: string;
    confidence: number;
    properties: { [key: string]: any };
  }[];
  metadata: {
    sourceText: string;
    processingTime: number;
    algorithm: string;
    confidence: number;
  };
}

// WebSocket message types
export interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'update' | 'expand' | 'process' | 'crawl' | 'error';
  payload: any;
  timestamp: string;
  id: string;
}

// Graph statistics
export interface GraphStatistics {
  nodeCount: number;
  relationshipCount: number;
  density: number;
  averageDegree: number;
  clusteringCoefficient: number;
  diameter: number;
  connectedComponents: number;
  stronglyConnectedComponents: number;
  centrality: {
    betweenness: { [nodeId: string]: number };
    closeness: { [nodeId: string]: number };
    eigenvector: { [nodeId: string]: number };
    pagerank: { [nodeId: string]: number };
  };
}

// Export capabilities
export interface ExportOptions {
  format: 'json' | 'csv' | 'graphml' | 'gexf' | 'cytoscape' | 'svg' | 'png';
  includeMetadata: boolean;
  filterOptions?: GraphFilters;
  layoutInfo?: boolean;
  compression?: boolean;
}

export interface ExportResult {
  data: string | Blob;
  filename: string;
  size: number;
  format: string;
  timestamp: string;
}

// Progressive loading
export interface LoadingStrategy {
  type: 'viewport' | 'importance' | 'distance' | 'cluster' | 'hybrid';
  parameters: {
    batchSize: number;
    threshold: number;
    priority: 'breadth' | 'depth' | 'importance';
  };
}

export interface LoadingState {
  isLoading: boolean;
  loadedNodes: Set<string>;
  loadedRelationships: Set<string>;
  pendingNodes: string[];
  pendingRelationships: string[];
  progress: number;
  strategy: LoadingStrategy;
}

// Collaboration features
export interface CollaborationState {
  isCollaborative: boolean;
  sessionId: string;
  users: {
    id: string;
    name: string;
    color: string;
    cursor?: { x: number; y: number };
    selection?: string[];
  }[];
  changes: {
    id: string;
    userId: string;
    type: 'create' | 'update' | 'delete' | 'move';
    target: string;
    timestamp: string;
    data: any;
  }[];
}

// Theme and styling
export interface GraphTheme {
  name: string;
  colors: {
    background: string;
    nodes: { [type: string]: string };
    relationships: { [type: string]: string };
    text: string;
    highlight: string;
    selection: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    sizes: { [element: string]: number };
  };
  effects: {
    shadows: boolean;
    gradients: boolean;
    animations: boolean;
    particles: boolean;
  };
}
