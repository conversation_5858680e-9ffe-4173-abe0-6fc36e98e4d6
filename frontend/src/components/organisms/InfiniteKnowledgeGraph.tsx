import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import * as d3 from 'd3';
import { io, Socket } from 'socket.io-client';
import {
  GraphData,
  GraphNode,
  GraphRelationship,
  GraphViewState,
  ClusterData,
  ExpansionOptions,
  GraphUpdateEvent
} from '@/types/graph';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Search,
  Filter,
  Settings,
  Play,
  Pause,
  Maximize2,
  Network,
  Brain,
  Sparkles,
  Target,
  Layers,
  Download,
  Upload,
  RefreshCw,
  Zap,
  Eye,
  EyeOff
} from 'lucide-react';

interface InfiniteKnowledgeGraphProps {
  initialData?: GraphData;
  width?: number;
  height?: number;
  autoExpansion?: boolean;
  expansionThreshold?: number;
  maxNodes?: number;
  onNodeSelect?: (node: GraphNode) => void;
  onGraphUpdate?: (data: GraphData) => void;
  className?: string;
}

interface GraphMetrics {
  totalNodes: number;
  totalRelationships: number;
  clusters: number;
  density: number;
  avgDegree: number;
  connectedComponents: number;
}

interface PerformanceMetrics {
  renderTime: number;
  updateTime: number;
  memoryUsage: number;
  fps: number;
}

const InfiniteKnowledgeGraph: React.FC<InfiniteKnowledgeGraphProps> = ({
  initialData,
  width = 1400,
  height = 900,
  autoExpansion = true,
  expansionThreshold = 0.7,
  maxNodes = 5000,
  onNodeSelect,
  onGraphUpdate,
  className = ''
}) => {
  // Refs
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const socketRef = useRef<Socket | null>(null);
  const simulationRef = useRef<d3.Simulation<GraphNode, GraphRelationship> | null>(null);
  const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);
  const animationFrameRef = useRef<number>();

  // State
  const [graphData, setGraphData] = useState<GraphData>(initialData || { nodes: [], relationships: [] });
  const [filteredData, setFilteredData] = useState<GraphData>(graphData);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);
  const [clusters, setClusters] = useState<ClusterData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAutoExpanding, setIsAutoExpanding] = useState(autoExpansion);
  const [showClusters, setShowClusters] = useState(false);
  const [showMetrics, setShowMetrics] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // View state
  const [viewState, setViewState] = useState<GraphViewState>({
    zoom: 1,
    pan: { x: 0, y: 0 },
    selectedNodes: new Set<string>(),
    highlightedNodes: new Set<string>(),
    showLabels: true,
    showRelationshipLabels: false,
    nodeSize: 8,
    linkDistance: 80,
    chargeStrength: -200,
    layoutMode: 'force',
    animationSpeed: 1,
    particleEffects: true,
    hoverEffects: true,
    clustering: {
      enabled: true,
      threshold: 0.6,
      algorithm: 'modularity'
    }
  });

  // Filters
  const [filters, setFilters] = useState({
    nodeTypes: [] as string[],
    relationshipTypes: [] as string[],
    confidenceThreshold: 0.5,
    strengthLevels: [] as string[],
    dateRange: null as [Date, Date] | null
  });

  // Metrics
  const [graphMetrics, setGraphMetrics] = useState<GraphMetrics>({
    totalNodes: 0,
    totalRelationships: 0,
    clusters: 0,
    density: 0,
    avgDegree: 0,
    connectedComponents: 0
  });

  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    updateTime: 0,
    memoryUsage: 0,
    fps: 0
  });

  // WebSocket connection
  useEffect(() => {
    const socket = io(`${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/api/graph`, {
      path: '/api/graph/socket.io',
      transports: ['websocket']
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      setIsConnected(true);
      console.log('🔌 Connected to graph WebSocket');
      
      // Subscribe to graph updates
      socket.emit('subscribe_graph_updates', {
        nodeTypes: filters.nodeTypes,
        relationshipTypes: filters.relationshipTypes,
        confidenceThreshold: filters.confidenceThreshold,
        autoExpansion: isAutoExpanding
      });
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      console.log('🔌 Disconnected from graph WebSocket');
    });

    socket.on('initial_graph_data', (data: GraphData) => {
      console.log('📊 Received initial graph data', data);
      setGraphData(data);
      setIsLoading(false);
    });

    socket.on('graph_update', (update: GraphUpdateEvent) => {
      console.log('🔄 Received graph update', update);
      handleGraphUpdate(update);
    });

    socket.on('graph_expanded', (data: any) => {
      console.log('🔍 Graph expanded', data);
      handleGraphExpansion(data);
    });

    socket.on('clustering_updated', (data: ClusterData[]) => {
      console.log('🎯 Clustering updated', data);
      setClusters(data);
    });

    socket.on('ai_processing_started', (data: any) => {
      console.log('🧠 AI processing started', data);
      setIsLoading(true);
    });

    socket.on('ai_processing_completed', (data: any) => {
      console.log('🧠 AI processing completed', data);
      setIsLoading(false);
    });

    socket.on('graph_error', (error: any) => {
      console.error('❌ Graph error', error);
      setIsLoading(false);
    });

    return () => {
      socket.disconnect();
    };
  }, [filters, isAutoExpanding]);

  // Handle graph updates from WebSocket
  const handleGraphUpdate = useCallback((update: GraphUpdateEvent) => {
    const startTime = performance.now();
    
    setGraphData(prevData => {
      let newData = { ...prevData };
      
      switch (update.type) {
        case 'node_created':
          if (update.data.nodes) {
            newData.nodes = [...prevData.nodes, ...update.data.nodes];
          }
          break;
          
        case 'relationship_created':
          if (update.data.relationships) {
            newData.relationships = [...prevData.relationships, ...update.data.relationships];
          }
          break;
          
        case 'node_updated':
          newData.nodes = prevData.nodes.map(node => 
            node.id === update.data.id ? { ...node, ...update.data } : node
          );
          break;
          
        case 'node_deleted':
          newData.nodes = prevData.nodes.filter(node => node.id !== update.data.id);
          newData.relationships = prevData.relationships.filter(rel => 
            rel.source !== update.data.id && rel.target !== update.data.id
          );
          break;
          
        default:
          return prevData;
      }
      
      // Trigger performance update
      const updateTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, updateTime }));
      
      return newData;
    });
  }, []);

  // Handle graph expansion
  const handleGraphExpansion = useCallback((expansionData: any) => {
    const { data, sourceNodeId, expansionType } = expansionData;
    
    setGraphData(prevData => {
      const newNodes = data.relatedNodes || [];
      const newRelationships = data.relationships || [];
      
      // Merge new data with existing, avoiding duplicates
      const existingNodeIds = new Set(prevData.nodes.map(n => n.id));
      const existingRelIds = new Set(prevData.relationships.map(r => r.id));
      
      const uniqueNewNodes = newNodes.filter((node: GraphNode) => !existingNodeIds.has(node.id));
      const uniqueNewRels = newRelationships.filter((rel: GraphRelationship) => !existingRelIds.has(rel.id));
      
      return {
        nodes: [...prevData.nodes, ...uniqueNewNodes],
        relationships: [...prevData.relationships, ...uniqueNewRels]
      };
    });
    
    // Animate expansion
    if (svgRef.current) {
      animateGraphExpansion(sourceNodeId, expansionType);
    }
  }, []);

  // Animate graph expansion
  const animateGraphExpansion = useCallback((sourceNodeId: string, expansionType: string) => {
    if (!svgRef.current) return;
    
    const svg = d3.select(svgRef.current);
    const sourceNode = svg.select(`[data-node-id="${sourceNodeId}"]`);
    
    if (sourceNode.empty()) return;
    
    // Create expansion wave effect
    const sourcePos = sourceNode.datum() as any;
    const wave = svg.append('circle')
      .attr('cx', sourcePos.x)
      .attr('cy', sourcePos.y)
      .attr('r', 0)
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 2)
      .attr('opacity', 0.8);
    
    wave.transition()
      .duration(1000)
      .attr('r', 100)
      .attr('opacity', 0)
      .remove();
    
    // Add sparkle effects
    if (viewState.particleEffects) {
      createSparkleEffect(sourcePos.x, sourcePos.y);
    }
  }, [viewState.particleEffects]);

  // Create sparkle effect
  const createSparkleEffect = useCallback((x: number, y: number) => {
    if (!svgRef.current) return;
    
    const svg = d3.select(svgRef.current);
    const sparkles = svg.append('g').attr('class', 'sparkles');
    
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * 2 * Math.PI;
      const distance = 30 + Math.random() * 20;
      const sparkleX = x + Math.cos(angle) * distance;
      const sparkleY = y + Math.sin(angle) * distance;
      
      const sparkle = sparkles.append('circle')
        .attr('cx', x)
        .attr('cy', y)
        .attr('r', 2)
        .attr('fill', '#fbbf24')
        .attr('opacity', 1);
      
      sparkle.transition()
        .duration(800)
        .attr('cx', sparkleX)
        .attr('cy', sparkleY)
        .attr('r', 0)
        .attr('opacity', 0)
        .remove();
    }
    
    setTimeout(() => sparkles.remove(), 1000);
  }, []);

  // Calculate graph metrics
  const calculateMetrics = useCallback((data: GraphData): GraphMetrics => {
    const nodeCount = data.nodes.length;
    const relCount = data.relationships.length;
    
    // Calculate degree for each node
    const degrees = new Map<string, number>();
    data.relationships.forEach(rel => {
      degrees.set(rel.source, (degrees.get(rel.source) || 0) + 1);
      degrees.set(rel.target, (degrees.get(rel.target) || 0) + 1);
    });
    
    const avgDegree = nodeCount > 0 ? Array.from(degrees.values()).reduce((a, b) => a + b, 0) / nodeCount : 0;
    const maxPossibleEdges = nodeCount * (nodeCount - 1) / 2;
    const density = maxPossibleEdges > 0 ? relCount / maxPossibleEdges : 0;
    
    return {
      totalNodes: nodeCount,
      totalRelationships: relCount,
      clusters: clusters.length,
      density,
      avgDegree,
      connectedComponents: 1 // Simplified - would need proper calculation
    };
  }, [clusters]);

  // Update metrics when data changes
  useEffect(() => {
    const metrics = calculateMetrics(graphData);
    setGraphMetrics(metrics);
    
    if (onGraphUpdate) {
      onGraphUpdate(graphData);
    }
  }, [graphData, calculateMetrics, onGraphUpdate]);

  // Apply filters
  useEffect(() => {
    let filtered = { ...graphData };
    
    // Apply node type filters
    if (filters.nodeTypes.length > 0) {
      filtered.nodes = filtered.nodes.filter(node => 
        filters.nodeTypes.some(type => node.labels?.includes(type))
      );
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered.nodes = filtered.nodes.filter(node =>
        node.properties?.name?.toLowerCase().includes(query) ||
        node.properties?.description?.toLowerCase().includes(query)
      );
    }
    
    // Apply confidence threshold
    filtered.nodes = filtered.nodes.filter(node => 
      (node.properties?.confidence || 1) >= filters.confidenceThreshold
    );
    
    // Filter relationships to only include those between filtered nodes
    const nodeIds = new Set(filtered.nodes.map(n => n.id));
    filtered.relationships = filtered.relationships.filter(rel =>
      nodeIds.has(rel.source) && nodeIds.has(rel.target)
    );
    
    setFilteredData(filtered);
  }, [graphData, filters, searchQuery]);

  // Auto-expansion logic
  useEffect(() => {
    if (!isAutoExpanding || !isConnected || !socketRef.current) return;
    
    const checkForExpansion = () => {
      // Find nodes with high interaction but low connection count
      const candidates = filteredData.nodes.filter(node => {
        const connections = filteredData.relationships.filter(rel =>
          rel.source === node.id || rel.target === node.id
        ).length;
        
        const importance = node.properties?.importance || 0;
        return connections < 3 && importance > expansionThreshold;
      });
      
      // Expand from the most important candidate
      if (candidates.length > 0) {
        const bestCandidate = candidates.reduce((best, current) => 
          (current.properties?.importance || 0) > (best.properties?.importance || 0) ? current : best
        );
        
        socketRef.current?.emit('expand_graph', {
          nodeId: bestCandidate.id,
          expansionType: 'related',
          limit: 10
        });
      }
    };
    
    const interval = setInterval(checkForExpansion, 5000);
    return () => clearInterval(interval);
  }, [filteredData, isAutoExpanding, isConnected, expansionThreshold]);

  // Expand graph manually
  const expandGraph = useCallback((nodeId: string, expansionType: string = 'related') => {
    if (!socketRef.current) return;
    
    socketRef.current.emit('expand_graph', {
      nodeId,
      expansionType,
      limit: 20
    });
  }, []);

  // Process text with AI
  const processWithAI = useCallback((text: string) => {
    if (!socketRef.current) return;
    
    socketRef.current.emit('process_with_ai', {
      text,
      sourceId: `manual_${Date.now()}`
    });
  }, []);

  // Start crawling
  const startCrawl = useCallback((url: string, options: any = {}) => {
    if (!socketRef.current) return;
    
    socketRef.current.emit('start_crawl', {
      url,
      options
    });
  }, []);

  // D3 visualization setup
  useEffect(() => {
    if (!svgRef.current || filteredData.nodes.length === 0) return;

    const startTime = performance.now();
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create main container with zoom
    const container = svg.append('g').attr('class', 'graph-container');

    // Setup zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 10])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
        setViewState(prev => ({
          ...prev,
          zoom: event.transform.k,
          pan: { x: event.transform.x, y: event.transform.y }
        }));
      });

    svg.call(zoom);
    zoomRef.current = zoom;

    // Create force simulation
    const simulation = d3.forceSimulation(filteredData.nodes as any)
      .force('link', d3.forceLink(filteredData.relationships)
        .id((d: any) => d.id)
        .distance(viewState.linkDistance)
        .strength(0.5))
      .force('charge', d3.forceManyBody().strength(viewState.chargeStrength))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(viewState.nodeSize + 5));

    simulationRef.current = simulation;

    // Create gradient definitions for enhanced visuals
    const defs = svg.append('defs');

    // Node gradients
    const nodeGradient = defs.append('radialGradient')
      .attr('id', 'nodeGradient')
      .attr('cx', '30%')
      .attr('cy', '30%');

    nodeGradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#ffffff')
      .attr('stop-opacity', 0.8);

    nodeGradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#000000')
      .attr('stop-opacity', 0.1);

    // Create links
    const links = container.selectAll('.link')
      .data(filteredData.relationships)
      .enter().append('line')
      .attr('class', 'link')
      .attr('stroke', d => getRelationshipColor(d.type))
      .attr('stroke-width', d => getRelationshipWidth(d.properties?.strength))
      .attr('stroke-opacity', 0.6)
      .attr('stroke-dasharray', d => d.type.includes('CONTRAINDICATED') ? '5,5' : 'none')
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation();
        console.log('Relationship clicked:', d);
      })
      .on('mouseenter', function(event, d) {
        d3.select(this).attr('stroke-opacity', 1);
        showTooltip(event, d, 'relationship');
      })
      .on('mouseleave', function() {
        d3.select(this).attr('stroke-opacity', 0.6);
        hideTooltip();
      });

    // Create nodes
    const nodes = container.selectAll('.node')
      .data(filteredData.nodes)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer')
      .call(d3.drag<SVGGElement, GraphNode>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          (d as any).fx = (d as any).x;
          (d as any).fy = (d as any).y;
        })
        .on('drag', (event, d) => {
          (d as any).fx = event.x;
          (d as any).fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          (d as any).fx = null;
          (d as any).fy = null;
        }));

    // Add circles to nodes
    nodes.append('circle')
      .attr('r', d => getNodeSize(d))
      .attr('fill', d => getNodeColor(d))
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2)
      .attr('filter', 'url(#nodeGradient)')
      .on('click', (event, d) => {
        event.stopPropagation();
        setSelectedNode(d);
        onNodeSelect?.(d);

        // Auto-expand on double click
        if (event.detail === 2 && isAutoExpanding) {
          expandGraph(d.id, 'related');
        }
      })
      .on('mouseenter', function(event, d) {
        setHoveredNode(d);

        if (viewState.hoverEffects) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr('r', getNodeSize(d) * 1.3)
            .attr('stroke-width', 4);
        }

        showTooltip(event, d, 'node');
        highlightConnectedNodes(d.id);
      })
      .on('mouseleave', function(event, d) {
        setHoveredNode(null);

        if (viewState.hoverEffects) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr('r', getNodeSize(d))
            .attr('stroke-width', 2);
        }

        hideTooltip();
        clearHighlights();
      });

    // Add labels to nodes
    if (viewState.showLabels) {
      nodes.append('text')
        .text(d => d.properties?.name || d.id)
        .attr('dy', d => -getNodeSize(d) - 5)
        .attr('text-anchor', 'middle')
        .attr('font-size', '11px')
        .attr('font-weight', 'bold')
        .attr('fill', '#374151')
        .attr('pointer-events', 'none')
        .style('text-shadow', '1px 1px 2px rgba(255,255,255,0.8)');
    }

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      nodes.attr('transform', (d: any) => `translate(${d.x},${d.y})`);
    });

    // Performance tracking
    const renderTime = performance.now() - startTime;
    setPerformanceMetrics(prev => ({ ...prev, renderTime }));

    return () => {
      simulation.stop();
    };
  }, [filteredData, viewState, width, height, isAutoExpanding, onNodeSelect]);

  // Helper functions for visualization
  const getNodeColor = (node: GraphNode): string => {
    const category = node.properties?.category || node.labels?.[0] || 'default';
    const colorMap: { [key: string]: string } = {
      'SUPPLEMENT': '#6366f1',
      'INGREDIENT': '#10b981',
      'CONDITION': '#f59e0b',
      'MECHANISM': '#8b5cf6',
      'DOSAGE': '#06b6d4',
      'STUDY': '#84cc16',
      'EFFECT': '#ef4444',
      'POPULATION': '#f97316',
      'default': '#6b7280'
    };
    return colorMap[category] || colorMap.default;
  };

  const getNodeSize = (node: GraphNode): number => {
    const baseSize = viewState.nodeSize;
    const importance = node.properties?.importance || 1;
    const connections = filteredData.relationships.filter(rel =>
      rel.source === node.id || rel.target === node.id
    ).length;

    return baseSize + Math.sqrt(connections) * 2 + importance * 3;
  };

  const getRelationshipColor = (type: string): string => {
    const colorMap: { [key: string]: string } = {
      'TREATS': '#10b981',
      'PREVENTS': '#059669',
      'ENHANCES': '#047857',
      'INHIBITS': '#dc2626',
      'SYNERGISTIC_WITH': '#8b5cf6',
      'CONTRAINDICATED_WITH': '#ef4444',
      'ACTS_VIA': '#6366f1',
      'AFFECTS_ABSORPTION': '#f59e0b',
      'default': '#6b7280'
    };
    return colorMap[type] || colorMap.default;
  };

  const getRelationshipWidth = (strength?: string): number => {
    const strengthMap: { [key: string]: number } = {
      'high': 4,
      'medium': 2.5,
      'low': 1.5,
      'default': 2
    };
    return strengthMap[strength || 'default'];
  };

  const highlightConnectedNodes = (nodeId: string) => {
    const connectedNodeIds = new Set<string>();
    filteredData.relationships.forEach(rel => {
      if (rel.source === nodeId) connectedNodeIds.add(rel.target);
      if (rel.target === nodeId) connectedNodeIds.add(rel.source);
    });

    setViewState(prev => ({
      ...prev,
      highlightedNodes: connectedNodeIds
    }));
  };

  const clearHighlights = () => {
    setViewState(prev => ({
      ...prev,
      highlightedNodes: new Set()
    }));
  };

  const showTooltip = (event: any, data: any, type: 'node' | 'relationship') => {
    // Tooltip implementation would go here
    console.log('Show tooltip:', type, data);
  };

  const hideTooltip = () => {
    // Hide tooltip implementation
  };

  return (
    <div className={`infinite-knowledge-graph ${className} flex flex-col h-full`} ref={containerRef}>
      {/* Top Control Bar */}
      <div className="flex items-center justify-between p-4 bg-white border-b shadow-sm">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-bold text-gray-800">Knowledge Graph</h2>
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "🟢 Live" : "🔴 Offline"}
          </Badge>
          <Badge variant="outline">
            {graphMetrics.totalNodes} nodes • {graphMetrics.totalRelationships} edges
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search nodes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* Auto-expansion toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              checked={isAutoExpanding}
              onCheckedChange={setIsAutoExpanding}
            />
            <span className="text-sm text-gray-600">Auto-expand</span>
          </div>

          {/* View controls */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowClusters(!showClusters)}
          >
            <Layers className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowMetrics(!showMetrics)}
          >
            <Target className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            <Maximize2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Side Panel */}
        <div className="w-80 bg-white border-r overflow-y-auto">
          <Tabs defaultValue="controls" className="h-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="controls">Controls</TabsTrigger>
              <TabsTrigger value="filters">Filters</TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="controls" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Visualization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <label className="text-xs text-gray-600">Node Size</label>
                    <Slider
                      value={[viewState.nodeSize]}
                      onValueChange={([value]) => setViewState(prev => ({ ...prev, nodeSize: value }))}
                      min={4}
                      max={20}
                      step={1}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <label className="text-xs text-gray-600">Link Distance</label>
                    <Slider
                      value={[viewState.linkDistance]}
                      onValueChange={([value]) => setViewState(prev => ({ ...prev, linkDistance: value }))}
                      min={20}
                      max={200}
                      step={10}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <label className="text-xs text-gray-600">Charge Strength</label>
                    <Slider
                      value={[Math.abs(viewState.chargeStrength)]}
                      onValueChange={([value]) => setViewState(prev => ({ ...prev, chargeStrength: -value }))}
                      min={50}
                      max={500}
                      step={25}
                      className="mt-1"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-600">Show Labels</span>
                    <Switch
                      checked={viewState.showLabels}
                      onCheckedChange={(checked) => setViewState(prev => ({ ...prev, showLabels: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-600">Hover Effects</span>
                    <Switch
                      checked={viewState.hoverEffects}
                      onCheckedChange={(checked) => setViewState(prev => ({ ...prev, hoverEffects: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-600">Particle Effects</span>
                    <Switch
                      checked={viewState.particleEffects}
                      onCheckedChange={(checked) => setViewState(prev => ({ ...prev, particleEffects: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>

              {showMetrics && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span>Nodes:</span>
                      <span>{graphMetrics.totalNodes}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Relationships:</span>
                      <span>{graphMetrics.totalRelationships}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Density:</span>
                      <span>{(graphMetrics.density * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Degree:</span>
                      <span>{graphMetrics.avgDegree.toFixed(1)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Render Time:</span>
                      <span>{performanceMetrics.renderTime.toFixed(1)}ms</span>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="filters" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Node Types</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {['SUPPLEMENT', 'INGREDIENT', 'CONDITION', 'MECHANISM', 'STUDY'].map(type => (
                      <div key={type} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={type}
                          checked={filters.nodeTypes.includes(type)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFilters(prev => ({ ...prev, nodeTypes: [...prev.nodeTypes, type] }));
                            } else {
                              setFilters(prev => ({ ...prev, nodeTypes: prev.nodeTypes.filter(t => t !== type) }));
                            }
                          }}
                        />
                        <label htmlFor={type} className="text-xs">{type}</label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Confidence Threshold</CardTitle>
                </CardHeader>
                <CardContent>
                  <Slider
                    value={[filters.confidenceThreshold]}
                    onValueChange={([value]) => setFilters(prev => ({ ...prev, confidenceThreshold: value }))}
                    min={0}
                    max={1}
                    step={0.1}
                    className="mt-2"
                  />
                  <div className="text-xs text-gray-600 mt-1">
                    {(filters.confidenceThreshold * 100).toFixed(0)}%
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="actions" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">AI Processing</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => {
                      const text = prompt("Enter text to process with AI:");
                      if (text) processWithAI(text);
                    }}
                    className="w-full"
                    size="sm"
                  >
                    <Brain className="w-4 h-4 mr-2" />
                    Process Text
                  </Button>

                  <Button
                    onClick={() => {
                      const url = prompt("Enter URL to crawl:");
                      if (url) startCrawl(url);
                    }}
                    className="w-full"
                    size="sm"
                    variant="outline"
                  >
                    <Network className="w-4 h-4 mr-2" />
                    Crawl Website
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Graph Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    onClick={() => {
                      if (selectedNode) {
                        expandGraph(selectedNode.id, 'related');
                      }
                    }}
                    disabled={!selectedNode}
                    className="w-full"
                    size="sm"
                    variant="outline"
                  >
                    <Zap className="w-4 h-4 mr-2" />
                    Expand Selected
                  </Button>

                  <Button
                    onClick={() => {
                      // Reset zoom and pan
                      if (zoomRef.current && svgRef.current) {
                        d3.select(svgRef.current)
                          .transition()
                          .duration(750)
                          .call(zoomRef.current.transform, d3.zoomIdentity);
                      }
                    }}
                    className="w-full"
                    size="sm"
                    variant="outline"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset View
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Graph Area */}
        <div className="flex-1 relative">
          <svg
            ref={svgRef}
            width={width}
            height={height}
            className="graph-svg bg-gradient-to-br from-slate-50 to-slate-100 w-full h-full"
          />

          {/* Loading overlay */}
          {isLoading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <div className="flex items-center space-x-3">
                  <RefreshCw className="animate-spin text-blue-500 w-6 h-6" />
                  <div>
                    <div className="font-medium">Processing graph data...</div>
                    <div className="text-sm text-gray-600">This may take a moment</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Selected Node Info */}
          {selectedNode && (
            <div className="absolute bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg max-w-sm">
              <h3 className="font-bold text-lg">{selectedNode.properties?.name || selectedNode.id}</h3>
              <p className="text-sm text-gray-600 mt-1">
                {selectedNode.properties?.description || 'No description available'}
              </p>
              <div className="flex items-center space-x-2 mt-2">
                <Badge variant="outline">
                  {selectedNode.labels?.[0] || 'Unknown'}
                </Badge>
                {selectedNode.properties?.confidence && (
                  <Badge variant="secondary">
                    {(selectedNode.properties.confidence * 100).toFixed(0)}% confidence
                  </Badge>
                )}
              </div>
              <Button
                onClick={() => expandGraph(selectedNode.id, 'related')}
                size="sm"
                className="mt-3 w-full"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Expand from this node
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InfiniteKnowledgeGraph;
