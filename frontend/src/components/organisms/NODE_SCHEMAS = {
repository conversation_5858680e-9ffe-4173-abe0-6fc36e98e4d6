// Core Entity Nodes
CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE;
CREATE CONSTRAINT ingredient_id IF NOT EXISTS FOR (i:Ingredient) REQUIRE i.id IS UNIQUE;
CREATE CONSTRAINT condition_id IF NOT EXISTS FOR (c:Condition) REQUIRE c.id IS UNIQUE;
CREATE CONSTRAINT mechanism_id IF NOT EXISTS FOR (m:Mechanism) REQUIRE m.id IS UNIQUE;
CREATE CONSTRAINT study_id IF NOT EXISTS FOR (st:Study) REQUIRE st.id IS UNIQUE;
CREATE CONSTRAINT effect_id IF NOT EXISTS FOR (e:Effect) REQUIRE e.id IS UNIQUE;
CREATE CONSTRAINT population_id IF NOT EXISTS FOR (p:Population) REQUIRE p.id IS UNIQUE;
CREATE CONSTRAINT dosage_id IF NOT EXISTS FOR (d:Dosage) REQUIRE d.id IS UNIQUE;

// Safety and Regulatory Nodes
CREATE CONSTRAINT safety_profile_id IF NOT EXISTS FOR (sp:SafetyProfile) REQUIRE sp.id IS UNIQUE;
CREATE CONSTRAINT interaction_id IF NOT EXISTS FOR (int:Interaction) REQUIRE int.id IS UNIQUE;
CREATE CONSTRAINT contraindication_id IF NOT EXISTS FOR (ci:Contraindication) REQUIRE ci.id IS UNIQUE;

// Research and Evidence Nodes
CREATE CONSTRAINT publication_id IF NOT EXISTS FOR (pub:Publication) REQUIRE pub.id IS UNIQUE;
CREATE CONSTRAINT clinical_trial_id IF NOT EXISTS FOR (ct:ClinicalTrial) REQUIRE ct.id IS UNIQUE;
CREATE CONSTRAINT evidence_id IF NOT EXISTS FOR (ev:Evidence) REQUIRE ev.id IS UNIQUE;
