import { 
  GraphData, 
  GraphNode, 
  GraphRelationship, 
  GraphUpdateEvent,
  LoadingStrategy,
  LoadingState,
  PerformanceMetrics,
  GraphStatistics,
  ExportOptions,
  ExportResult
} from '@/types/graph';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccess: number;
}

interface GraphCache {
  nodes: Map<string, CacheEntry<GraphNode>>;
  relationships: Map<string, CacheEntry<GraphRelationship>>;
  queries: Map<string, CacheEntry<any>>;
  expansions: Map<string, CacheEntry<GraphData>>;
}

interface PerformanceConfig {
  maxNodes: number;
  maxRelationships: number;
  renderThreshold: number;
  updateBatchSize: number;
  cacheSize: number;
  cacheTTL: number;
}

export class GraphService {
  private cache: GraphCache;
  private loadingState: LoadingState;
  private performanceConfig: PerformanceConfig;
  private eventListeners: Map<string, Function[]>;
  private updateQueue: GraphUpdateEvent[];
  private isProcessingUpdates: boolean;
  private performanceMetrics: PerformanceMetrics;
  private abortController: AbortController;

  constructor(config?: Partial<PerformanceConfig>) {
    this.cache = {
      nodes: new Map(),
      relationships: new Map(),
      queries: new Map(),
      expansions: new Map()
    };

    this.performanceConfig = {
      maxNodes: 5000,
      maxRelationships: 10000,
      renderThreshold: 1000,
      updateBatchSize: 50,
      cacheSize: 1000,
      cacheTTL: 300000, // 5 minutes
      ...config
    };

    this.loadingState = {
      isLoading: false,
      loadedNodes: new Set(),
      loadedRelationships: new Set(),
      pendingNodes: [],
      pendingRelationships: [],
      progress: 0,
      strategy: {
        type: 'viewport',
        parameters: {
          batchSize: 100,
          threshold: 0.8,
          priority: 'importance'
        }
      }
    };

    this.eventListeners = new Map();
    this.updateQueue = [];
    this.isProcessingUpdates = false;
    this.abortController = new AbortController();

    this.performanceMetrics = {
      renderTime: 0,
      updateTime: 0,
      memoryUsage: 0,
      fps: 0,
      nodeCount: 0,
      relationshipCount: 0,
      visibleNodes: 0,
      visibleRelationships: 0
    };

    this.startPerformanceMonitoring();
    this.startCacheCleanup();
  }

  // Cache Management
  private setCacheEntry<T>(cache: Map<string, CacheEntry<T>>, key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.performanceConfig.cacheTTL,
      accessCount: 1,
      lastAccess: Date.now()
    };

    cache.set(key, entry);

    // Cleanup if cache is too large
    if (cache.size > this.performanceConfig.cacheSize) {
      this.cleanupCache(cache);
    }
  }

  private getCacheEntry<T>(cache: Map<string, CacheEntry<T>>, key: string): T | null {
    const entry = cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      cache.delete(key);
      return null;
    }

    entry.accessCount++;
    entry.lastAccess = now;
    return entry.data;
  }

  private cleanupCache<T>(cache: Map<string, CacheEntry<T>>): void {
    const now = Date.now();
    const entries = Array.from(cache.entries());
    
    // Remove expired entries
    entries.forEach(([key, entry]) => {
      if (now - entry.timestamp > entry.ttl) {
        cache.delete(key);
      }
    });

    // If still too large, remove least recently used
    if (cache.size > this.performanceConfig.cacheSize) {
      const sortedEntries = entries
        .filter(([key]) => cache.has(key))
        .sort((a, b) => a[1].lastAccess - b[1].lastAccess);

      const toRemove = sortedEntries.slice(0, cache.size - this.performanceConfig.cacheSize);
      toRemove.forEach(([key]) => cache.delete(key));
    }
  }

  // Data Management
  public async loadGraphData(options?: {
    nodeTypes?: string[];
    limit?: number;
    strategy?: LoadingStrategy;
  }): Promise<GraphData> {
    const cacheKey = JSON.stringify(options);
    const cached = this.getCacheEntry(this.cache.queries, cacheKey);
    if (cached) return cached;

    this.loadingState.isLoading = true;
    this.emit('loadingStateChanged', this.loadingState);

    try {
      const response = await fetch('/api/graph/data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(options),
        signal: this.abortController.signal
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data: GraphData = await response.json();
      
      // Cache individual nodes and relationships
      data.nodes.forEach(node => {
        this.setCacheEntry(this.cache.nodes, node.id, node);
        this.loadingState.loadedNodes.add(node.id);
      });

      data.relationships.forEach(rel => {
        this.setCacheEntry(this.cache.relationships, rel.id, rel);
        this.loadingState.loadedRelationships.add(rel.id);
      });

      // Cache the query result
      this.setCacheEntry(this.cache.queries, cacheKey, data);

      this.updatePerformanceMetrics(data);
      return data;

    } catch (error) {
      console.error('Failed to load graph data:', error);
      throw error;
    } finally {
      this.loadingState.isLoading = false;
      this.emit('loadingStateChanged', this.loadingState);
    }
  }

  public async expandGraph(nodeId: string, expansionType: string, options?: {
    limit?: number;
    depth?: number;
    filters?: any;
  }): Promise<GraphData> {
    const cacheKey = `expand_${nodeId}_${expansionType}_${JSON.stringify(options)}`;
    const cached = this.getCacheEntry(this.cache.expansions, cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch('/api/graph/expand', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nodeId, expansionType, options }),
        signal: this.abortController.signal
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data: GraphData = await response.json();
      this.setCacheEntry(this.cache.expansions, cacheKey, data);

      // Update loading state
      data.nodes.forEach(node => this.loadingState.loadedNodes.add(node.id));
      data.relationships.forEach(rel => this.loadingState.loadedRelationships.add(rel.id));

      this.emit('graphExpanded', { nodeId, expansionType, data });
      return data;

    } catch (error) {
      console.error('Failed to expand graph:', error);
      throw error;
    }
  }

  // Progressive Loading
  public async loadNodesInViewport(viewport: {
    x: number;
    y: number;
    width: number;
    height: number;
    zoom: number;
  }): Promise<GraphNode[]> {
    const visibleNodes = await this.getNodesInViewport(viewport);
    const unloadedNodes = visibleNodes.filter(nodeId => !this.loadingState.loadedNodes.has(nodeId));

    if (unloadedNodes.length === 0) return [];

    const batchSize = this.loadingState.strategy.parameters.batchSize;
    const batch = unloadedNodes.slice(0, batchSize);

    try {
      const response = await fetch('/api/graph/nodes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nodeIds: batch }),
        signal: this.abortController.signal
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const nodes: GraphNode[] = await response.json();
      
      nodes.forEach(node => {
        this.setCacheEntry(this.cache.nodes, node.id, node);
        this.loadingState.loadedNodes.add(node.id);
      });

      this.updateLoadingProgress();
      return nodes;

    } catch (error) {
      console.error('Failed to load nodes in viewport:', error);
      return [];
    }
  }

  private async getNodesInViewport(viewport: any): Promise<string[]> {
    // This would calculate which nodes are visible in the current viewport
    // For now, return empty array as placeholder
    return [];
  }

  private updateLoadingProgress(): void {
    const totalNodes = this.loadingState.loadedNodes.size + this.loadingState.pendingNodes.length;
    const loadedNodes = this.loadingState.loadedNodes.size;
    this.loadingState.progress = totalNodes > 0 ? loadedNodes / totalNodes : 1;
    
    this.emit('loadingProgressChanged', this.loadingState.progress);
  }

  // Update Processing
  public queueUpdate(update: GraphUpdateEvent): void {
    this.updateQueue.push(update);
    
    if (!this.isProcessingUpdates) {
      this.processUpdateQueue();
    }
  }

  private async processUpdateQueue(): Promise<void> {
    if (this.isProcessingUpdates || this.updateQueue.length === 0) return;

    this.isProcessingUpdates = true;
    const startTime = performance.now();

    try {
      const batchSize = this.performanceConfig.updateBatchSize;
      const batch = this.updateQueue.splice(0, batchSize);

      for (const update of batch) {
        await this.processUpdate(update);
      }

      const updateTime = performance.now() - startTime;
      this.performanceMetrics.updateTime = updateTime;

      // Continue processing if there are more updates
      if (this.updateQueue.length > 0) {
        setTimeout(() => this.processUpdateQueue(), 10);
      }

    } catch (error) {
      console.error('Error processing update queue:', error);
    } finally {
      this.isProcessingUpdates = false;
    }
  }

  private async processUpdate(update: GraphUpdateEvent): Promise<void> {
    switch (update.type) {
      case 'node_created':
      case 'node_updated':
        if (update.data.nodes) {
          update.data.nodes.forEach((node: GraphNode) => {
            this.setCacheEntry(this.cache.nodes, node.id, node);
            this.loadingState.loadedNodes.add(node.id);
          });
        }
        break;

      case 'node_deleted':
        if (update.data.nodeId) {
          this.cache.nodes.delete(update.data.nodeId);
          this.loadingState.loadedNodes.delete(update.data.nodeId);
        }
        break;

      case 'relationship_created':
      case 'relationship_updated':
        if (update.data.relationships) {
          update.data.relationships.forEach((rel: GraphRelationship) => {
            this.setCacheEntry(this.cache.relationships, rel.id, rel);
            this.loadingState.loadedRelationships.add(rel.id);
          });
        }
        break;

      case 'relationship_deleted':
        if (update.data.relationshipId) {
          this.cache.relationships.delete(update.data.relationshipId);
          this.loadingState.loadedRelationships.delete(update.data.relationshipId);
        }
        break;
    }

    this.emit('graphUpdated', update);
  }

  // Performance Monitoring
  private startPerformanceMonitoring(): void {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        this.performanceMetrics.fps = frameCount;
        frameCount = 0;
        lastTime = currentTime;
        
        this.emit('performanceMetricsUpdated', this.performanceMetrics);
      }
      
      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }

  private updatePerformanceMetrics(data: GraphData): void {
    this.performanceMetrics.nodeCount = data.nodes.length;
    this.performanceMetrics.relationshipCount = data.relationships.length;
    
    // Estimate memory usage (rough calculation)
    const nodeMemory = data.nodes.length * 200; // ~200 bytes per node
    const relMemory = data.relationships.length * 150; // ~150 bytes per relationship
    this.performanceMetrics.memoryUsage = nodeMemory + relMemory;
  }

  private startCacheCleanup(): void {
    setInterval(() => {
      this.cleanupCache(this.cache.nodes);
      this.cleanupCache(this.cache.relationships);
      this.cleanupCache(this.cache.queries);
      this.cleanupCache(this.cache.expansions);
    }, 60000); // Cleanup every minute
  }

  // Event System
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  // Utility Methods
  public getLoadingState(): LoadingState {
    return { ...this.loadingState };
  }

  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  public getCacheStats(): {
    nodes: number;
    relationships: number;
    queries: number;
    expansions: number;
  } {
    return {
      nodes: this.cache.nodes.size,
      relationships: this.cache.relationships.size,
      queries: this.cache.queries.size,
      expansions: this.cache.expansions.size
    };
  }

  public clearCache(): void {
    this.cache.nodes.clear();
    this.cache.relationships.clear();
    this.cache.queries.clear();
    this.cache.expansions.clear();
    
    this.emit('cacheCleared');
  }

  public destroy(): void {
    this.abortController.abort();
    this.clearCache();
    this.eventListeners.clear();
    this.updateQueue.length = 0;
  }
}
