import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { logger } from '../utils/logger';
import { GraphService } from './GraphService';
import { Gemma3Neo4jProcessor } from './Gemma3Neo4jProcessor';
import { CrawlService } from './CrawlService';

interface GraphUpdateEvent {
  type: 'node_created' | 'node_updated' | 'node_deleted' | 'relationship_created' | 'relationship_updated' | 'relationship_deleted' | 'graph_expanded' | 'clustering_updated';
  data: any;
  timestamp: string;
  source: 'manual' | 'crawl' | 'ai_processing' | 'auto_inference';
  confidence?: number;
}

interface ClientSubscription {
  socketId: string;
  filters: {
    nodeTypes?: string[];
    relationshipTypes?: string[];
    confidenceThreshold?: number;
    autoExpansion?: boolean;
  };
  lastUpdate: Date;
}

export class GraphWebSocketService {
  private io: SocketIOServer;
  private graphService: GraphService;
  private gemmaProcessor: Gemma3Neo4jProcessor;
  private crawlService: CrawlService;
  private subscriptions: Map<string, ClientSubscription> = new Map();
  private updateQueue: GraphUpdateEvent[] = [];
  private isProcessingQueue = false;

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      path: '/api/graph/socket.io'
    });

    this.graphService = new GraphService();
    this.gemmaProcessor = new Gemma3Neo4jProcessor();
    this.crawlService = new CrawlService();

    this.setupEventHandlers();
    this.startUpdateProcessor();
    
    logger.info('🔌 Graph WebSocket service initialized');
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket) => {
      logger.info(`📡 Graph client connected: ${socket.id}`);

      // Handle client subscription
      socket.on('subscribe_graph_updates', (filters) => {
        this.subscriptions.set(socket.id, {
          socketId: socket.id,
          filters: filters || {},
          lastUpdate: new Date()
        });
        
        logger.info(`📋 Client ${socket.id} subscribed to graph updates`, { filters });
        
        // Send initial graph data
        this.sendInitialGraphData(socket, filters);
      });

      // Handle real-time graph expansion requests
      socket.on('expand_graph', async (data) => {
        try {
          const { nodeId, expansionType, limit } = data;
          logger.info(`🔍 Expanding graph from node ${nodeId}`, { expansionType, limit });
          
          const expandedData = await this.expandGraphFromNode(nodeId, expansionType, limit);
          
          // Broadcast expansion to all subscribed clients
          this.broadcastUpdate({
            type: 'graph_expanded',
            data: expandedData,
            timestamp: new Date().toISOString(),
            source: 'manual'
          });
          
        } catch (error) {
          logger.error('Failed to expand graph', error);
          socket.emit('graph_error', { message: 'Failed to expand graph', error: error.message });
        }
      });

      // Handle AI processing requests
      socket.on('process_with_ai', async (data) => {
        try {
          const { text, sourceId } = data;
          logger.info(`🧠 Processing text with AI`, { textLength: text.length, sourceId });
          
          socket.emit('ai_processing_started', { sourceId });
          
          const result = await this.gemmaProcessor.processTextToGraph(text, sourceId);
          
          // Broadcast new nodes and relationships
          this.broadcastUpdate({
            type: 'node_created',
            data: result,
            timestamp: new Date().toISOString(),
            source: 'ai_processing',
            confidence: result.gemma_confidence
          });
          
          socket.emit('ai_processing_completed', { result, sourceId });
          
        } catch (error) {
          logger.error('Failed to process with AI', error);
          socket.emit('ai_processing_error', { message: 'AI processing failed', error: error.message });
        }
      });

      // Handle crawling requests
      socket.on('start_crawl', async (data) => {
        try {
          const { url, options } = data;
          logger.info(`🕷️ Starting crawl`, { url, options });
          
          socket.emit('crawl_started', { url });
          
          const crawlResult = await this.crawlService.crawlSinglePage({
            url,
            type: 'single_page',
            options
          });
          
          // Process crawled content with AI
          if (crawlResult.content) {
            const aiResult = await this.gemmaProcessor.processTextToGraph(
              crawlResult.content,
              `crawl_${Date.now()}`
            );
            
            this.broadcastUpdate({
              type: 'graph_expanded',
              data: {
                crawlResult,
                aiResult,
                source: url
              },
              timestamp: new Date().toISOString(),
              source: 'crawl',
              confidence: aiResult.gemma_confidence
            });
          }
          
          socket.emit('crawl_completed', { result: crawlResult, url });
          
        } catch (error) {
          logger.error('Failed to crawl', error);
          socket.emit('crawl_error', { message: 'Crawling failed', error: error.message });
        }
      });

      // Handle automatic relationship inference
      socket.on('infer_relationships', async (data) => {
        try {
          const { nodeIds, algorithm } = data;
          logger.info(`🔗 Inferring relationships`, { nodeIds, algorithm });
          
          const inferredRelationships = await this.inferRelationships(nodeIds, algorithm);
          
          this.broadcastUpdate({
            type: 'relationship_created',
            data: inferredRelationships,
            timestamp: new Date().toISOString(),
            source: 'auto_inference'
          });
          
        } catch (error) {
          logger.error('Failed to infer relationships', error);
          socket.emit('inference_error', { message: 'Relationship inference failed', error: error.message });
        }
      });

      // Handle clustering requests
      socket.on('update_clustering', async (data) => {
        try {
          const { algorithm, threshold, options } = data;
          logger.info(`🎯 Updating clustering`, { algorithm, threshold });
          
          const clusteringResult = await this.performAdvancedClustering(algorithm, threshold, options);
          
          this.broadcastUpdate({
            type: 'clustering_updated',
            data: clusteringResult,
            timestamp: new Date().toISOString(),
            source: 'manual'
          });
          
        } catch (error) {
          logger.error('Failed to update clustering', error);
          socket.emit('clustering_error', { message: 'Clustering failed', error: error.message });
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.subscriptions.delete(socket.id);
        logger.info(`📡 Graph client disconnected: ${socket.id}`);
      });
    });
  }

  private async sendInitialGraphData(socket: any, filters: any): Promise<void> {
    try {
      const graphData = await this.graphService.getGraphData({
        nodeTypes: filters.nodeTypes,
        relationshipTypes: filters.relationshipTypes,
        limit: filters.limit || 100
      });
      
      socket.emit('initial_graph_data', graphData);
      logger.info(`📊 Sent initial graph data to ${socket.id}`, { 
        nodes: graphData.nodes.length, 
        relationships: graphData.relationships.length 
      });
      
    } catch (error) {
      logger.error('Failed to send initial graph data', error);
      socket.emit('graph_error', { message: 'Failed to load initial data', error: error.message });
    }
  }

  private async expandGraphFromNode(nodeId: string, expansionType: string, limit: number = 20): Promise<any> {
    try {
      // Get related nodes based on expansion type
      let relatedData;
      
      switch (expansionType) {
        case 'related':
          relatedData = await this.graphService.getNodeRelationships(nodeId, {
            direction: 'both',
            limit
          });
          break;
          
        case 'similar':
          // Use AI to find similar nodes
          const node = await this.graphService.getNodeById(nodeId);
          relatedData = await this.findSimilarNodes(node, limit);
          break;
          
        case 'interactions':
          relatedData = await this.graphService.getNodeRelationships(nodeId, {
            direction: 'both',
            types: ['INTERACTS_WITH', 'SYNERGISTIC_WITH', 'CONTRAINDICATED_WITH'],
            limit
          });
          break;
          
        case 'studies':
          relatedData = await this.graphService.getNodeRelationships(nodeId, {
            direction: 'both',
            types: ['STUDIED_IN', 'SUPPORTS', 'CONTRADICTS'],
            limit
          });
          break;
          
        default:
          throw new Error(`Unknown expansion type: ${expansionType}`);
      }
      
      return {
        sourceNodeId: nodeId,
        expansionType,
        data: relatedData,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      logger.error('Failed to expand graph from node', error);
      throw error;
    }
  }

  private async findSimilarNodes(node: any, limit: number): Promise<any> {
    // Implementation for finding similar nodes using AI embeddings
    // This would use vector similarity search in Weaviate or similar
    // For now, return placeholder
    return {
      similarNodes: [],
      similarity_scores: []
    };
  }

  private async inferRelationships(nodeIds: string[], algorithm: string): Promise<any> {
    // Implementation for automatic relationship inference
    // This would use various algorithms like:
    // - Co-occurrence analysis
    // - Semantic similarity
    // - Pattern matching
    // - ML-based prediction
    
    return {
      inferredRelationships: [],
      confidence_scores: []
    };
  }

  private async performAdvancedClustering(algorithm: string, threshold: number, options: any): Promise<any> {
    // Implementation for advanced clustering algorithms
    // - Community detection
    // - Hierarchical clustering
    // - Density-based clustering
    // - Graph neural networks
    
    return {
      clusters: [],
      metrics: {}
    };
  }

  public broadcastUpdate(update: GraphUpdateEvent): void {
    this.updateQueue.push(update);
    
    if (!this.isProcessingQueue) {
      this.processUpdateQueue();
    }
  }

  private async processUpdateQueue(): Promise<void> {
    if (this.isProcessingQueue || this.updateQueue.length === 0) {
      return;
    }
    
    this.isProcessingQueue = true;
    
    try {
      while (this.updateQueue.length > 0) {
        const update = this.updateQueue.shift()!;
        
        // Filter and send to subscribed clients
        for (const [socketId, subscription] of this.subscriptions) {
          if (this.shouldSendUpdate(update, subscription)) {
            this.io.to(socketId).emit('graph_update', update);
          }
        }
        
        // Small delay to prevent overwhelming clients
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    } catch (error) {
      logger.error('Error processing update queue', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  private shouldSendUpdate(update: GraphUpdateEvent, subscription: ClientSubscription): boolean {
    // Apply filters to determine if update should be sent to this client
    const { filters } = subscription;
    
    // Check confidence threshold
    if (filters.confidenceThreshold && update.confidence && update.confidence < filters.confidenceThreshold) {
      return false;
    }
    
    // Check node types filter
    if (filters.nodeTypes && update.data.nodeType && !filters.nodeTypes.includes(update.data.nodeType)) {
      return false;
    }
    
    // Check relationship types filter
    if (filters.relationshipTypes && update.data.relationshipType && !filters.relationshipTypes.includes(update.data.relationshipType)) {
      return false;
    }
    
    return true;
  }

  private startUpdateProcessor(): void {
    // Process updates every 100ms
    setInterval(() => {
      if (this.updateQueue.length > 0) {
        this.processUpdateQueue();
      }
    }, 100);
  }

  public getConnectedClients(): number {
    return this.subscriptions.size;
  }

  public getUpdateQueueSize(): number {
    return this.updateQueue.length;
  }
}
