import { Request, Response, NextFunction } from 'express';
import { validationResult, ValidationError } from 'express-validator';
import { logger } from '../utils/logger';

export interface ValidationErrorResponse {
  success: false;
  error: {
    message: string;
    statusCode: number;
    timestamp: string;
    path: string;
    method: string;
    details: {
      field: string;
      message: string;
      value?: any;
    }[];
  };
}

/**
 * Middleware to validate request data using express-validator
 * Should be used after validation rules are defined
 */
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const validationErrors = errors.array().map((error: ValidationError) => ({
      field: 'field' in error ? error.field : 'unknown',
      message: error.msg,
      value: 'value' in error ? error.value : undefined
    }));

    const errorResponse: ValidationErrorResponse = {
      success: false,
      error: {
        message: 'Validation failed',
        statusCode: 400,
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method,
        details: validationErrors
      }
    };

    logger.warn('Validation failed', {
      path: req.path,
      method: req.method,
      errors: validationErrors,
      body: req.body,
      query: req.query,
      params: req.params
    });

    res.status(400).json(errorResponse);
    return;
  }

  next();
};

/**
 * Custom validation middleware for specific business logic
 */
export const validateBusinessRules = (rules: ((req: Request) => string | null)[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: string[] = [];

    for (const rule of rules) {
      const error = rule(req);
      if (error) {
        errors.push(error);
      }
    }

    if (errors.length > 0) {
      const errorResponse: ValidationErrorResponse = {
        success: false,
        error: {
          message: 'Business rule validation failed',
          statusCode: 422,
          timestamp: new Date().toISOString(),
          path: req.path,
          method: req.method,
          details: errors.map(error => ({
            field: 'business_rule',
            message: error
          }))
        }
      };

      logger.warn('Business rule validation failed', {
        path: req.path,
        method: req.method,
        errors,
        body: req.body
      });

      res.status(422).json(errorResponse);
      return;
    }

    next();
  };
};

/**
 * Validation rule: Check if URL is valid and accessible
 */
export const validateUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
};

/**
 * Validation rule: Check if crawl options are valid
 */
export const validateCrawlOptions = (options: any): string | null => {
  if (!options) return null;

  if (options.maxDepth && (options.maxDepth < 1 || options.maxDepth > 5)) {
    return 'maxDepth must be between 1 and 5';
  }

  if (options.maxPages && (options.maxPages < 1 || options.maxPages > 1000)) {
    return 'maxPages must be between 1 and 1000';
  }

  if (options.chunkSize && (options.chunkSize < 100 || options.chunkSize > 10000)) {
    return 'chunkSize must be between 100 and 10000';
  }

  return null;
};

/**
 * Validation rule: Check if node types are valid
 */
export const validateNodeTypes = (nodeTypes: string[]): string | null => {
  if (!Array.isArray(nodeTypes)) {
    return 'nodeTypes must be an array';
  }

  const validTypes = [
    'SUPPLEMENT',
    'INGREDIENT', 
    'CONDITION',
    'MECHANISM',
    'DOSAGE',
    'STUDY',
    'EFFECT',
    'POPULATION'
  ];

  const invalidTypes = nodeTypes.filter(type => !validTypes.includes(type));
  if (invalidTypes.length > 0) {
    return `Invalid node types: ${invalidTypes.join(', ')}. Valid types are: ${validTypes.join(', ')}`;
  }

  return null;
};

/**
 * Validation rule: Check if relationship types are valid
 */
export const validateRelationshipTypes = (relationshipTypes: string[]): string | null => {
  if (!Array.isArray(relationshipTypes)) {
    return 'relationshipTypes must be an array';
  }

  const validTypes = [
    'TREATS',
    'PREVENTS',
    'ENHANCES',
    'INHIBITS',
    'SYNERGISTIC_WITH',
    'CONTRAINDICATED_WITH',
    'ACTS_VIA',
    'AFFECTS_ABSORPTION',
    'STUDIED_IN',
    'SUPPORTS',
    'CONTRADICTS'
  ];

  const invalidTypes = relationshipTypes.filter(type => !validTypes.includes(type));
  if (invalidTypes.length > 0) {
    return `Invalid relationship types: ${invalidTypes.join(', ')}. Valid types are: ${validTypes.join(', ')}`;
  }

  return null;
};

/**
 * Validation rule: Check if confidence threshold is valid
 */
export const validateConfidenceThreshold = (threshold: number): string | null => {
  if (typeof threshold !== 'number') {
    return 'Confidence threshold must be a number';
  }

  if (threshold < 0 || threshold > 1) {
    return 'Confidence threshold must be between 0 and 1';
  }

  return null;
};

/**
 * Validation rule: Check if text input is valid for AI processing
 */
export const validateTextInput = (text: string): string | null => {
  if (typeof text !== 'string') {
    return 'Text input must be a string';
  }

  if (text.trim().length === 0) {
    return 'Text input cannot be empty';
  }

  if (text.length > 50000) {
    return 'Text input is too long (maximum 50,000 characters)';
  }

  return null;
};

/**
 * Validation rule: Check if pagination parameters are valid
 */
export const validatePagination = (page?: number, limit?: number): string | null => {
  if (page !== undefined) {
    if (!Number.isInteger(page) || page < 1) {
      return 'Page must be a positive integer';
    }
  }

  if (limit !== undefined) {
    if (!Number.isInteger(limit) || limit < 1 || limit > 1000) {
      return 'Limit must be a positive integer between 1 and 1000';
    }
  }

  return null;
};

/**
 * Validation rule: Check if date range is valid
 */
export const validateDateRange = (startDate?: string, endDate?: string): string | null => {
  if (startDate && !Date.parse(startDate)) {
    return 'Start date is not a valid date';
  }

  if (endDate && !Date.parse(endDate)) {
    return 'End date is not a valid date';
  }

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start > end) {
      return 'Start date must be before end date';
    }

    const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    if (end.getTime() - start.getTime() > maxRange) {
      return 'Date range cannot exceed 1 year';
    }
  }

  return null;
};

/**
 * Common business rules for crawl requests
 */
export const crawlBusinessRules = [
  (req: Request) => {
    const { url } = req.body;
    if (!validateUrl(url)) {
      return 'Invalid or inaccessible URL';
    }
    return null;
  },
  (req: Request) => {
    const { options } = req.body;
    return validateCrawlOptions(options);
  }
];

/**
 * Common business rules for graph requests
 */
export const graphBusinessRules = [
  (req: Request) => {
    const { nodeTypes } = req.body;
    if (nodeTypes) {
      return validateNodeTypes(nodeTypes);
    }
    return null;
  },
  (req: Request) => {
    const { relationshipTypes } = req.body;
    if (relationshipTypes) {
      return validateRelationshipTypes(relationshipTypes);
    }
    return null;
  },
  (req: Request) => {
    const { confidenceThreshold } = req.body;
    if (confidenceThreshold !== undefined) {
      return validateConfidenceThreshold(confidenceThreshold);
    }
    return null;
  }
];

/**
 * Common business rules for AI processing requests
 */
export const aiProcessingBusinessRules = [
  (req: Request) => {
    const { text } = req.body;
    return validateTextInput(text);
  }
];
