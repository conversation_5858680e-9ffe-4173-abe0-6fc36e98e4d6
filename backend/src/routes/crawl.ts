import { Router, Request, Response } from 'express';
import { CrawlService } from '../services/CrawlService';
import { logger } from '../utils/logger';
import { validateRequest } from '../middleware/validation';
import { body, query } from 'express-validator';

const router = Router();
const crawlService = new CrawlService();

// Validation schemas
const crawlSinglePageSchema = [
  body('url').isURL().withMessage('Valid URL is required'),
  body('options.maxDepth').optional().isInt({ min: 1, max: 5 }).withMessage('Max depth must be between 1 and 5'),
  body('options.chunkSize').optional().isInt({ min: 100, max: 5000 }).withMessage('Chunk size must be between 100 and 5000'),
  body('options.extractImages').optional().isBoolean().withMessage('Extract images must be boolean'),
];

const crawlRecursiveSchema = [
  body('url').isURL().withMessage('Valid URL is required'),
  body('options.maxDepth').optional().isInt({ min: 1, max: 3 }).withMessage('Max depth must be between 1 and 3'),
  body('options.maxPages').optional().isInt({ min: 1, max: 100 }).withMessage('Max pages must be between 1 and 100'),
  body('options.chunkSize').optional().isInt({ min: 100, max: 5000 }).withMessage('Chunk size must be between 100 and 5000'),
];

const crawlSitemapSchema = [
  body('url').isURL().withMessage('Valid sitemap URL is required'),
  body('options.maxPages').optional().isInt({ min: 1, max: 200 }).withMessage('Max pages must be between 1 and 200'),
  body('options.chunkSize').optional().isInt({ min: 100, max: 5000 }).withMessage('Chunk size must be between 100 and 5000'),
];

/**
 * @route POST /api/crawl/single
 * @desc Crawl a single page
 * @access Public
 */
router.post('/single', crawlSinglePageSchema, validateRequest, async (req: Request, res: Response) => {
  try {
    const { url, options = {} } = req.body;
    
    logger.info('Starting single page crawl', { url, options });
    
    const result = await crawlService.crawlSinglePage({
      url,
      type: 'single_page',
      options
    });
    
    logger.info('Single page crawl completed', { 
      url, 
      contentLength: result.content.length,
      chunksCount: result.chunks.length,
      crawlTime: result.crawlTime
    });
    
    res.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    logger.error('Single page crawl failed', { error: error.message, url: req.body.url });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to crawl page',
        details: error.message
      }
    });
  }
});

/**
 * @route POST /api/crawl/recursive
 * @desc Crawl website recursively
 * @access Public
 */
router.post('/recursive', crawlRecursiveSchema, validateRequest, async (req: Request, res: Response) => {
  try {
    const { url, options = {} } = req.body;
    
    logger.info('Starting recursive crawl', { url, options });
    
    const results = await crawlService.crawlRecursive({
      url,
      type: 'recursive',
      options
    });
    
    logger.info('Recursive crawl completed', { 
      url, 
      pagesCount: results.length,
      totalContentLength: results.reduce((sum, r) => sum + r.content.length, 0)
    });
    
    res.json({
      success: true,
      data: results
    });
    
  } catch (error) {
    logger.error('Recursive crawl failed', { error: error.message, url: req.body.url });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to crawl website recursively',
        details: error.message
      }
    });
  }
});

/**
 * @route POST /api/crawl/sitemap
 * @desc Crawl from sitemap
 * @access Public
 */
router.post('/sitemap', crawlSitemapSchema, validateRequest, async (req: Request, res: Response) => {
  try {
    const { url, options = {} } = req.body;
    
    logger.info('Starting sitemap crawl', { url, options });
    
    const results = await crawlService.crawlSitemap({
      url,
      type: 'sitemap',
      options
    });
    
    logger.info('Sitemap crawl completed', { 
      url, 
      pagesCount: results.length,
      totalContentLength: results.reduce((sum, r) => sum + r.content.length, 0)
    });
    
    res.json({
      success: true,
      data: results
    });
    
  } catch (error) {
    logger.error('Sitemap crawl failed', { error: error.message, url: req.body.url });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to crawl sitemap',
        details: error.message
      }
    });
  }
});

/**
 * @route POST /api/crawl/markdown
 * @desc Crawl markdown/text content
 * @access Public
 */
router.post('/markdown', crawlSinglePageSchema, validateRequest, async (req: Request, res: Response) => {
  try {
    const { url, options = {} } = req.body;
    
    logger.info('Starting markdown crawl', { url, options });
    
    const result = await crawlService.crawlMarkdown({
      url,
      type: 'llms_txt',
      options
    });
    
    logger.info('Markdown crawl completed', { 
      url, 
      contentLength: result.content.length,
      chunksCount: result.chunks.length,
      crawlTime: result.crawlTime
    });
    
    res.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    logger.error('Markdown crawl failed', { error: error.message, url: req.body.url });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to crawl markdown content',
        details: error.message
      }
    });
  }
});

/**
 * @route GET /api/crawl/progress/:crawlId
 * @desc Get crawl progress
 * @access Public
 */
router.get('/progress/:crawlId', async (req: Request, res: Response) => {
  try {
    const { crawlId } = req.params;
    
    const progress = await crawlService.getCrawlProgress(crawlId);
    
    res.json({
      success: true,
      data: progress
    });
    
  } catch (error) {
    logger.error('Failed to get crawl progress', { error: error.message, crawlId: req.params.crawlId });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get crawl progress',
        details: error.message
      }
    });
  }
});

/**
 * @route DELETE /api/crawl/:crawlId
 * @desc Cancel active crawl
 * @access Public
 */
router.delete('/:crawlId', async (req: Request, res: Response) => {
  try {
    const { crawlId } = req.params;
    
    const cancelled = await crawlService.cancelCrawl(crawlId);
    
    res.json({
      success: true,
      data: { cancelled }
    });
    
  } catch (error) {
    logger.error('Failed to cancel crawl', { error: error.message, crawlId: req.params.crawlId });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to cancel crawl',
        details: error.message
      }
    });
  }
});

/**
 * @route GET /api/crawl/health
 * @desc Check crawl service health
 * @access Public
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const isHealthy = await crawlService.healthCheck();
    
    res.json({
      success: true,
      data: {
        healthy: isHealthy,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Crawl health check failed', { error: error.message });
    res.status(500).json({
      success: false,
      error: {
        message: 'Crawl health check failed',
        details: error.message
      }
    });
  }
});

export default router;
