{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:00:02:02"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"AG-UI WebSocket connection established: 02a669f3-9be5-4cb6-8380-b54ccc2b1022","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: 7f506975-c485-4bde-81e8-24f490386965","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: bb950cbf-7b54-40d2-8889-871a5767e56d","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: c347bfba-1100-4984-a472-40a2c5d3ad43","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: de876eda-d1bf-4510-9345-ceb50e24635c","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection closed: de876eda-d1bf-4510-9345-ceb50e24635c","timestamp":"2025-06-04 00:00:12:012"}
{"level":"info","message":"AG-UI WebSocket connection closed: 7f506975-c485-4bde-81e8-24f490386965","timestamp":"2025-06-04 00:00:12:012"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"AG-UI WebSocket connection established: 2aa295d9-6d40-43ef-9317-c72e9e265c06","timestamp":"2025-06-04 00:00:49:049"}
{"level":"info","message":"AG-UI WebSocket connection established: 31d93ac7-8214-4ada-879c-e8e8b9adc093","timestamp":"2025-06-04 00:00:51:051"}
{"level":"info","message":"AG-UI WebSocket connection closed: 02a669f3-9be5-4cb6-8380-b54ccc2b1022","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: c347bfba-1100-4984-a472-40a2c5d3ad43","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2aa295d9-6d40-43ef-9317-c72e9e265c06","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: bb950cbf-7b54-40d2-8889-871a5767e56d","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: 08f32c04-82e3-49bd-b44a-b40fa5d62b0a","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 08f32c04-82e3-49bd-b44a-b40fa5d62b0a","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: bff6b134-3870-4f06-b475-13ebc45e88cd","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: 609d873e-3e30-4b79-b681-5e6be82a00bb","timestamp":"2025-06-04 00:01:35:135"}
{"level":"info","message":"Starting Gemma analysis for supplement: magnaz i żelazo","timestamp":"2025-06-04 00:02:12:212"}
{"level":"error","message":"Error parsing Gemma analysis response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseAnalysisResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:265:27)\n    at GemmaService.analyzeSupplementData (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:74:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:108:24)","timestamp":"2025-06-04 00:02:12:212"}
{"level":"info","message":"Gemma analysis completed for magnaz i żelazo","timestamp":"2025-06-04 00:02:12:212"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 609d873e-3e30-4b79-b681-5e6be82a00bb-2dabf81a-c7b1-4de3-b72d-c48fa94c258e: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:03:54:354"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:03:54:354"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:01:41"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"AG-UI WebSocket connection established: 77543004-c179-4f25-81c3-add26191fd3b","timestamp":"2025-06-04 00:04:02:42"}
{"level":"info","message":"AG-UI WebSocket connection established: 63cc952c-98e6-4cab-b18d-2417fccb069e","timestamp":"2025-06-04 00:04:02:42"}
{"level":"info","message":"AG-UI WebSocket connection established: 7687dccd-94fb-4149-8103-ed92e2d82cea","timestamp":"2025-06-04 00:04:02:42"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:05:45"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:05:45"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:11:411"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"AG-UI WebSocket connection established: 3922e5cc-2d7d-452c-ac16-5a7f64ca48d8","timestamp":"2025-06-04 00:04:12:412"}
{"level":"info","message":"AG-UI WebSocket connection established: a1cb6a28-0509-4a96-a7e9-b2de729078c0","timestamp":"2025-06-04 00:04:12:412"}
{"level":"info","message":"AG-UI WebSocket connection established: 74e02068-be2e-47a6-ad58-483c913193c6","timestamp":"2025-06-04 00:04:12:412"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:32:432"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:32:432"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:38:438"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"AG-UI WebSocket connection established: 51e1a6e3-6e29-4ca7-8015-1a5f05a75342","timestamp":"2025-06-04 00:04:39:439"}
{"level":"info","message":"AG-UI WebSocket connection established: d3406bd6-a21c-4341-9542-ac6723a1a69a","timestamp":"2025-06-04 00:04:39:439"}
{"level":"info","message":"AG-UI WebSocket connection established: 23abbf4c-2c40-4fe8-aa1e-5a6805863047","timestamp":"2025-06-04 00:04:39:439"}
{"level":"info","message":"Starting Gemma analysis for supplement: magnaz i żelazo","timestamp":"2025-06-04 00:04:48:448"}
{"level":"warn","message":"Empty or undefined response from Gemma for magnaz i żelazo, using fallback","timestamp":"2025-06-04 00:04:48:448"}
{"level":"info","message":"Gemma analysis completed for magnaz i żelazo","timestamp":"2025-06-04 00:04:48:448"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:48:448"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:49:449"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:49:449"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:04:49:449"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:04:49:449"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 23abbf4c-2c40-4fe8-aa1e-5a6805863047-4ae2a3d6-50b6-470b-b7cc-8f3432fb10ca: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:04:49:449"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:05:15:515"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:05:15:515"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:21:521"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:21:521"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:21:521"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"AG-UI WebSocket connection established: 94cfa245-245b-4528-8bb1-84c09600504c","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"AG-UI WebSocket connection established: d06f8408-94b5-419f-8639-20ea31d37149","timestamp":"2025-06-04 00:05:48:548"}
{"level":"info","message":"AG-UI WebSocket connection established: 5f2fbbdd-ae1e-4b19-be32-0adfca58905b","timestamp":"2025-06-04 00:06:10:610"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5f2fbbdd-ae1e-4b19-be32-0adfca58905b","timestamp":"2025-06-04 00:06:10:610"}
{"level":"info","message":"AG-UI WebSocket connection established: cc03b53a-e63d-43df-b285-d7ad78e4b150","timestamp":"2025-06-04 00:06:10:610"}
{"level":"info","message":"AG-UI WebSocket connection established: 32902675-7b7b-4239-9b58-9f83dd0e5eb5","timestamp":"2025-06-04 00:06:11:611"}
{"level":"info","message":"AG-UI WebSocket connection closed: cc03b53a-e63d-43df-b285-d7ad78e4b150","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection closed: 32902675-7b7b-4239-9b58-9f83dd0e5eb5","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection closed: d06f8408-94b5-419f-8639-20ea31d37149","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: 461a8f32-d565-451b-a043-a1a3adf86f37","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: 398620da-d06e-4cac-9038-63eab89a87fd","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: eb34f5ce-72fd-467a-bc93-1348a845c337","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection closed: 398620da-d06e-4cac-9038-63eab89a87fd","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: 98b4a112-f40d-4176-a039-4bfc7286e4c4","timestamp":"2025-06-04 00:06:19:619"}
{"level":"info","message":"AG-UI WebSocket connection established: 34d65f9d-d1f3-4f9d-a8a4-01d064b6f0e1","timestamp":"2025-06-04 00:06:20:620"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:06:22:622"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*tyrozyna*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"tyrozyna"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"tyrozyna","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:22:622"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for d06f8408-94b5-419f-8639-20ea31d37149-6792fd4d-eb4b-4082-ba19-40e222d018f0: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:22:622"}
{"level":"info","message":"AG-UI WebSocket connection established: 920f2ee3-1c46-4a6a-94d6-161732332d96","timestamp":"2025-06-04 00:06:23:623"}
{"level":"info","message":"AG-UI WebSocket connection closed: 920f2ee3-1c46-4a6a-94d6-161732332d96","timestamp":"2025-06-04 00:06:23:623"}
{"level":"info","message":"AG-UI WebSocket connection established: 2e80a59b-49f4-4353-9117-6f689d01112c","timestamp":"2025-06-04 00:06:23:623"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*tyrozyna*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"tyrozyna"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"tyrozyna","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:24:624"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 32902675-7b7b-4239-9b58-9f83dd0e5eb5-e92a63b9-2948-422f-96e4-2f4882224230: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"AG-UI WebSocket connection established: d810e5e4-aa48-4e21-b4cc-aba9b6b9a213","timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"AG-UI WebSocket connection closed: d810e5e4-aa48-4e21-b4cc-aba9b6b9a213","timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"AG-UI WebSocket connection closed: 461a8f32-d565-451b-a043-a1a3adf86f37","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2e80a59b-49f4-4353-9117-6f689d01112c","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: 98b4a112-f40d-4176-a039-4bfc7286e4c4","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection established: 623f6d6d-c7c4-4ff5-b432-0031c67f45b9","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: 623f6d6d-c7c4-4ff5-b432-0031c67f45b9","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection established: 0ef190d2-a77b-44e7-a080-abec4276cf00","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: eb34f5ce-72fd-467a-bc93-1348a845c337","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection closed: 34d65f9d-d1f3-4f9d-a8a4-01d064b6f0e1","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: 2051f1b2-f7ae-4bfb-a9b8-df4de39e3e1e","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2051f1b2-f7ae-4bfb-a9b8-df4de39e3e1e","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: a1d811fc-9e24-4f93-ad2e-e09282d3b97f","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: 30b5ec3b-e427-474b-b007-8167c6956b9b","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: de7e25d3-31a3-4198-a223-bef50eac5bf2","timestamp":"2025-06-04 00:06:29:629"}
{"contentLength":"123","duration":1550,"ip":"::1","level":"info","message":"API: GET /api/research/health - 200 (1550ms)","method":"GET","statusCode":200,"timestamp":"2025-06-04 00:06:51:651","url":"/api/research/health","userAgent":"curl/8.5.0"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:32:732"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:32:732"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:39:739"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"AG-UI WebSocket connection established: 86e5ebae-0418-404e-8874-8ff47aaf01d7","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: b1506864-59c2-4885-bce1-a1e66c0b4e05","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: ba410cf2-326c-4039-a560-249938fbece8","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: 708a1167-0e5b-4415-a50b-9d608ba7f807","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: d2a39886-c4d1-4dc2-ac25-44c97566d430","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:45:745"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:45:745"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:52:752"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"AG-UI WebSocket connection established: cfca790b-696e-41f5-b0a5-1aab133cea9d","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"AG-UI WebSocket connection established: 3a6285cd-3868-47e2-b367-c1c40003add3","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"AG-UI WebSocket connection established: 5e746ff7-2348-429d-9c95-a976ff85649a","timestamp":"2025-06-04 00:07:53:753"}
{"level":"info","message":"AG-UI WebSocket connection established: e2e391f9-6d8f-4bd8-ab57-c772a720763d","timestamp":"2025-06-04 00:07:53:753"}
{"level":"info","message":"AG-UI WebSocket connection established: 6722fcaa-265f-4619-bc18-85379f199e3e","timestamp":"2025-06-04 00:07:53:753"}
{"contentLength":"263766","duration":7717,"ip":"::1","level":"info","message":"API: POST /api/research/web-search - 200 (7717ms)","method":"POST","statusCode":200,"timestamp":"2025-06-04 00:08:10:810","url":"/api/research/web-search","userAgent":"curl/8.5.0"}
{"contentLength":"123","duration":1872,"ip":"::1","level":"info","message":"API: GET /api/research/health - 200 (1872ms)","method":"GET","statusCode":200,"timestamp":"2025-06-04 00:08:24:824","url":"/api/research/health","userAgent":"curl/8.5.0"}
{"level":"info","message":"AG-UI WebSocket connection closed: cfca790b-696e-41f5-b0a5-1aab133cea9d","timestamp":"2025-06-04 00:09:22:922"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5e746ff7-2348-429d-9c95-a976ff85649a","timestamp":"2025-06-04 00:09:22:922"}
{"level":"info","message":"AG-UI WebSocket connection established: 2a685efb-93a0-497a-b4b5-582dd05439f6","timestamp":"2025-06-04 00:09:23:923"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2a685efb-93a0-497a-b4b5-582dd05439f6","timestamp":"2025-06-04 00:09:23:923"}
{"level":"info","message":"AG-UI WebSocket connection established: 3f041fa3-5d1a-4adb-8351-448ffd1c913e","timestamp":"2025-06-04 00:09:23:923"}
{"level":"info","message":"AG-UI WebSocket connection established: 5cf66980-52ae-4bdc-8eae-855ca46f43c6","timestamp":"2025-06-04 00:09:24:924"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:09:29:929"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:09:29:929"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:09:29:929"}
{"duration":309,"level":"info","message":"Graph: Search nodes (309ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":585,"level":"info","message":"Graph: Create node (1 nodes) (585ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":268,"level":"info","message":"Graph: Search nodes (268ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":40,"level":"info","message":"Graph: Create node (1 nodes) (40ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":213,"level":"info","message":"Graph: Create relationship (1 relationships) (213ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":57,"level":"info","message":"Graph: Search nodes (57ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":38,"level":"info","message":"Graph: Create node (1 nodes) (38ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":78,"level":"info","message":"Graph: Create relationship (1 relationships) (78ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":36,"level":"info","message":"Graph: Create node (1 nodes) (36ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":57,"level":"info","message":"Graph: Create relationship (1 relationships) (57ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":1764,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (1764ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:09:31:931"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:09:31:931"}
{"level":"info","message":"Agent completed for 5cf66980-52ae-4bdc-8eae-855ca46f43c6-be6385e1-ecc7-4d76-98b8-39069fc83541","timestamp":"2025-06-04 00:09:31:931"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5cf66980-52ae-4bdc-8eae-855ca46f43c6","timestamp":"2025-06-04 00:09:31:931"}
{"level":"info","message":"AG-UI WebSocket connection established: 287ca91c-5bc9-4549-acc2-8573542b9a59","timestamp":"2025-06-04 00:11:22:1122"}
{"level":"info","message":"AG-UI WebSocket connection closed: 287ca91c-5bc9-4549-acc2-8573542b9a59","timestamp":"2025-06-04 00:11:22:1122"}
{"level":"info","message":"AG-UI WebSocket connection established: 9201042a-646c-4f3a-a7eb-911399e2945e","timestamp":"2025-06-04 00:11:22:1122"}
{"level":"info","message":"AG-UI WebSocket connection established: 189002f1-c6a5-4c1b-a14b-74caab0c9f45","timestamp":"2025-06-04 00:11:23:1123"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:11:34:1134"}
{"duration":35,"level":"info","message":"Graph: Create node (1 nodes) (35ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":10,"level":"info","message":"Graph: Create node (1 nodes) (10ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create relationship (1 relationships) (9ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":11,"level":"info","message":"Graph: Create node (1 nodes) (11ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create relationship (1 relationships) (9ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":12,"level":"info","message":"Graph: Create relationship (1 relationships) (12ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":174,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (174ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:11:34:1134"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"info","message":"Agent completed for 189002f1-c6a5-4c1b-a14b-74caab0c9f45-9872456b-0c7a-4eea-94be-36b2d28fe82c","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3f041fa3-5d1a-4adb-8351-448ffd1c913e","timestamp":"2025-06-04 00:12:24:1224"}
{"level":"info","message":"AG-UI WebSocket connection closed: 189002f1-c6a5-4c1b-a14b-74caab0c9f45","timestamp":"2025-06-04 00:12:24:1224"}
{"level":"info","message":"AG-UI WebSocket connection closed: 9201042a-646c-4f3a-a7eb-911399e2945e","timestamp":"2025-06-04 00:12:24:1224"}
{"level":"info","message":"AG-UI WebSocket connection established: d6119859-065b-419d-bf77-a9a5436dec21","timestamp":"2025-06-04 00:12:26:1226"}
{"level":"info","message":"AG-UI WebSocket connection closed: d6119859-065b-419d-bf77-a9a5436dec21","timestamp":"2025-06-04 00:12:26:1226"}
{"level":"info","message":"AG-UI WebSocket connection established: 5275195d-7dd1-43d4-aea2-4abbb89835e3","timestamp":"2025-06-04 00:12:26:1226"}
{"level":"info","message":"AG-UI WebSocket connection established: b998e0fd-95c5-4994-a052-419eb180a5ac","timestamp":"2025-06-04 00:12:27:1227"}
{"level":"info","message":"AG-UI WebSocket connection established: e667f732-8e9d-4093-9d60-0db1519c7609","timestamp":"2025-06-04 00:12:37:1237"}
{"level":"info","message":"AG-UI WebSocket connection closed: e667f732-8e9d-4093-9d60-0db1519c7609","timestamp":"2025-06-04 00:12:37:1237"}
{"level":"info","message":"AG-UI WebSocket connection established: 2f36685c-4aef-4ed0-97ee-750f23f5da86","timestamp":"2025-06-04 00:12:37:1237"}
{"level":"info","message":"AG-UI WebSocket connection established: 0bdb4e72-730e-4f42-b434-b13dd3344a1f","timestamp":"2025-06-04 00:12:38:1238"}
{"level":"info","message":"AG-UI WebSocket connection established: 7a850ccd-578d-460b-a485-4b8cbab50f5d","timestamp":"2025-06-04 00:13:03:133"}
{"level":"info","message":"AG-UI WebSocket connection closed: 7a850ccd-578d-460b-a485-4b8cbab50f5d","timestamp":"2025-06-04 00:13:03:133"}
{"level":"info","message":"AG-UI WebSocket connection established: 506c99d3-c5a0-4a0b-ac64-30b593429b96","timestamp":"2025-06-04 00:13:03:133"}
{"level":"info","message":"AG-UI WebSocket connection established: 3997b81d-c838-4ac5-a029-a10af7bc56cf","timestamp":"2025-06-04 00:13:04:134"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:13:22:1322"}
{"duration":19,"level":"info","message":"Graph: Create node (1 nodes) (19ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":6,"level":"info","message":"Graph: Create node (1 nodes) (6ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create relationship (1 relationships) (7ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":12,"level":"info","message":"Graph: Create node (1 nodes) (12ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":13,"level":"info","message":"Graph: Create relationship (1 relationships) (13ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":143,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (143ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:13:22:1322"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"info","message":"Agent completed for 3997b81d-c838-4ac5-a029-a10af7bc56cf-56d5a175-1d53-4eb2-bddf-67a00f5bb0f1","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:18:35:1835"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:19:33:1933"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:24:18:2418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:25:05:255"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"AG-UI WebSocket connection established: a7030ca1-6b15-4427-9330-ef7eb021c14e","timestamp":"2025-06-04 00:26:00:260"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:26:09:269"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:26:09:269"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:26:09:269"}
{"duration":26,"level":"info","message":"Graph: Create node (1 nodes) (26ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":17,"level":"info","message":"Graph: Create node (1 nodes) (17ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":44,"level":"info","message":"Graph: Create relationship (1 relationships) (44ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":18,"level":"info","message":"Graph: Create node (1 nodes) (18ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":50,"level":"info","message":"Graph: Create relationship (1 relationships) (50ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":17,"level":"info","message":"Graph: Create node (1 nodes) (17ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":43,"level":"info","message":"Graph: Create relationship (1 relationships) (43ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":7,"level":"info","message":"Graph: Create relationship (1 relationships) (7ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":9,"level":"info","message":"Graph: Create relationship (1 relationships) (9ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":292,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (292ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:26:09:269"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:26:09:269"}
{"level":"info","message":"Agent completed for a7030ca1-6b15-4427-9330-ef7eb021c14e-74cc1731-cca0-4762-a499-b31ec414aa68","timestamp":"2025-06-04 00:26:09:269"}
{"level":"info","message":"AG-UI WebSocket connection closed: a7030ca1-6b15-4427-9330-ef7eb021c14e","timestamp":"2025-06-04 00:31:04:314"}
{"level":"info","message":"AG-UI WebSocket connection established: 2e2d42ea-fca6-4665-be85-f84f65c039fd","timestamp":"2025-06-04 00:31:05:315"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2e2d42ea-fca6-4665-be85-f84f65c039fd","timestamp":"2025-06-04 00:31:05:315"}
{"level":"info","message":"AG-UI WebSocket connection established: 1f9184da-ee8d-49f1-875e-92fc3321a927","timestamp":"2025-06-04 00:31:05:315"}
{"level":"info","message":"AG-UI WebSocket connection established: fb244c59-be18-4882-8728-969fccd28093","timestamp":"2025-06-04 00:31:06:316"}
{"level":"info","message":"AG-UI WebSocket connection closed: 1f9184da-ee8d-49f1-875e-92fc3321a927","timestamp":"2025-06-04 00:31:19:3119"}
{"level":"info","message":"AG-UI WebSocket connection closed: fb244c59-be18-4882-8728-969fccd28093","timestamp":"2025-06-04 00:31:19:3119"}
{"level":"info","message":"AG-UI WebSocket connection established: f982e7af-2c0e-4b58-a447-d09cc28ca667","timestamp":"2025-06-04 00:31:20:3120"}
{"level":"info","message":"AG-UI WebSocket connection closed: f982e7af-2c0e-4b58-a447-d09cc28ca667","timestamp":"2025-06-04 00:31:20:3120"}
{"level":"info","message":"AG-UI WebSocket connection established: 799bdda8-2fc4-4586-81db-8eefcfa937ea","timestamp":"2025-06-04 00:31:20:3120"}
{"level":"info","message":"AG-UI WebSocket connection established: a44f1cc1-54b0-4d31-b897-e0e936edf4d2","timestamp":"2025-06-04 00:31:21:3121"}
{"level":"info","message":"AG-UI WebSocket connection closed: 799bdda8-2fc4-4586-81db-8eefcfa937ea","timestamp":"2025-06-04 00:31:39:3139"}
{"level":"info","message":"AG-UI WebSocket connection closed: a44f1cc1-54b0-4d31-b897-e0e936edf4d2","timestamp":"2025-06-04 00:31:39:3139"}
{"level":"info","message":"AG-UI WebSocket connection established: 5ad601e9-588f-4a6d-a034-711d466c24a2","timestamp":"2025-06-04 00:31:40:3140"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5ad601e9-588f-4a6d-a034-711d466c24a2","timestamp":"2025-06-04 00:31:40:3140"}
{"level":"info","message":"AG-UI WebSocket connection established: c749989f-76ca-4c1d-872d-20cb4276324c","timestamp":"2025-06-04 00:31:40:3140"}
{"level":"info","message":"AG-UI WebSocket connection established: 0edbb2fe-366f-4ef7-9ae4-d54349e673ea","timestamp":"2025-06-04 00:31:41:3141"}
{"level":"info","message":"AG-UI WebSocket connection closed: c749989f-76ca-4c1d-872d-20cb4276324c","timestamp":"2025-06-04 00:31:51:3151"}
{"level":"info","message":"AG-UI WebSocket connection closed: 0edbb2fe-366f-4ef7-9ae4-d54349e673ea","timestamp":"2025-06-04 00:31:51:3151"}
{"level":"info","message":"AG-UI WebSocket connection established: 5469188b-c656-4104-a36e-5d95b0dafcfc","timestamp":"2025-06-04 00:31:52:3152"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5469188b-c656-4104-a36e-5d95b0dafcfc","timestamp":"2025-06-04 00:31:52:3152"}
{"level":"info","message":"AG-UI WebSocket connection established: 6b2ef453-85e6-453c-9be5-9d07f5c39404","timestamp":"2025-06-04 00:31:52:3152"}
{"level":"info","message":"AG-UI WebSocket connection established: aeb7647e-6e81-4cb2-b823-c05c382d9809","timestamp":"2025-06-04 00:31:53:3153"}
{"level":"info","message":"AG-UI WebSocket connection closed: 6b2ef453-85e6-453c-9be5-9d07f5c39404","timestamp":"2025-06-04 00:32:02:322"}
{"level":"info","message":"AG-UI WebSocket connection closed: aeb7647e-6e81-4cb2-b823-c05c382d9809","timestamp":"2025-06-04 00:32:02:322"}
{"level":"info","message":"AG-UI WebSocket connection established: fe15eed3-9c93-4b32-8d2d-0f5a352e9c82","timestamp":"2025-06-04 00:32:02:322"}
{"level":"info","message":"AG-UI WebSocket connection closed: fe15eed3-9c93-4b32-8d2d-0f5a352e9c82","timestamp":"2025-06-04 00:32:02:322"}
{"level":"info","message":"AG-UI WebSocket connection established: c57b6522-4469-4638-aad3-c177d42358ea","timestamp":"2025-06-04 00:32:02:322"}
{"level":"info","message":"AG-UI WebSocket connection established: bb1d0afe-d783-4ed9-8483-af4df604d0ff","timestamp":"2025-06-04 00:32:03:323"}
{"level":"info","message":"AG-UI WebSocket connection closed: c57b6522-4469-4638-aad3-c177d42358ea","timestamp":"2025-06-04 00:32:11:3211"}
{"level":"info","message":"AG-UI WebSocket connection closed: bb1d0afe-d783-4ed9-8483-af4df604d0ff","timestamp":"2025-06-04 00:32:11:3211"}
{"level":"info","message":"AG-UI WebSocket connection established: b88d0d7f-2e0f-4edf-b9fb-1a8d2ddc8240","timestamp":"2025-06-04 00:32:11:3211"}
{"level":"info","message":"AG-UI WebSocket connection closed: b88d0d7f-2e0f-4edf-b9fb-1a8d2ddc8240","timestamp":"2025-06-04 00:32:11:3211"}
{"level":"info","message":"AG-UI WebSocket connection established: 6ed2cd4d-9041-4d58-ab2a-92e897ec2c2c","timestamp":"2025-06-04 00:32:11:3211"}
{"level":"info","message":"AG-UI WebSocket connection established: ca1e595f-df7c-4c42-b3f5-af6a99c3d162","timestamp":"2025-06-04 00:32:12:3212"}
{"level":"info","message":"AG-UI WebSocket connection established: b8615a27-f236-423f-8c49-27b4698e05c1","timestamp":"2025-06-04 00:35:08:358"}
{"level":"info","message":"AG-UI WebSocket connection closed: b8615a27-f236-423f-8c49-27b4698e05c1","timestamp":"2025-06-04 00:35:08:358"}
{"level":"info","message":"AG-UI WebSocket connection established: a0f80a35-6512-4dff-9252-c6acaa90c904","timestamp":"2025-06-04 00:35:08:358"}
{"level":"info","message":"AG-UI WebSocket connection established: 16c3f169-6b56-4fda-884f-10f818a22835","timestamp":"2025-06-04 00:35:09:359"}
{"level":"info","message":"AG-UI WebSocket connection closed: 16c3f169-6b56-4fda-884f-10f818a22835","timestamp":"2025-06-04 00:35:27:3527"}
{"level":"info","message":"AG-UI WebSocket connection established: 403bc542-1b46-483e-bf2f-4cc5a7b176ec","timestamp":"2025-06-04 00:35:29:3529"}
{"level":"info","message":"AG-UI WebSocket connection closed: 403bc542-1b46-483e-bf2f-4cc5a7b176ec","timestamp":"2025-06-04 00:35:29:3529"}
{"level":"info","message":"AG-UI WebSocket connection established: 7b10536a-4735-4ad5-99d9-ff876e1a14cc","timestamp":"2025-06-04 00:35:29:3529"}
{"level":"info","message":"AG-UI WebSocket connection established: 5eeefffd-1d06-40c3-a8aa-f28c6cc4a2bf","timestamp":"2025-06-04 00:35:30:3530"}
{"level":"info","message":"AG-UI WebSocket connection established: a60d6f14-f75e-4d0c-82a7-fec88a991a34","timestamp":"2025-06-04 00:35:43:3543"}
{"level":"info","message":"AG-UI WebSocket connection closed: a60d6f14-f75e-4d0c-82a7-fec88a991a34","timestamp":"2025-06-04 00:35:43:3543"}
{"level":"info","message":"AG-UI WebSocket connection established: fc279eb6-f52c-4861-a7b1-1e830fc62a58","timestamp":"2025-06-04 00:35:43:3543"}
{"level":"info","message":"AG-UI WebSocket connection established: d9be9b18-386c-4c7d-ba8d-0a4f68e1b73a","timestamp":"2025-06-04 00:35:44:3544"}
{"level":"info","message":"AG-UI WebSocket connection closed: fc279eb6-f52c-4861-a7b1-1e830fc62a58","timestamp":"2025-06-04 00:39:02:392"}
{"level":"info","message":"AG-UI WebSocket connection closed: d9be9b18-386c-4c7d-ba8d-0a4f68e1b73a","timestamp":"2025-06-04 00:39:02:392"}
{"level":"info","message":"AG-UI WebSocket connection established: 42c292e8-8f7a-4453-bab3-b359f5cee8d2","timestamp":"2025-06-04 00:39:03:393"}
{"level":"info","message":"AG-UI WebSocket connection established: c9a23c36-67ee-4c24-9eb9-71b0140d7e23","timestamp":"2025-06-04 00:39:03:393"}
{"level":"info","message":"AG-UI WebSocket connection closed: 42c292e8-8f7a-4453-bab3-b359f5cee8d2","timestamp":"2025-06-04 00:39:03:393"}
{"level":"info","message":"AG-UI WebSocket connection established: b21d08d0-e4e2-49fa-9974-28ff90242cc6","timestamp":"2025-06-04 00:39:04:394"}
{"level":"info","message":"AG-UI WebSocket connection closed: c9a23c36-67ee-4c24-9eb9-71b0140d7e23","timestamp":"2025-06-04 00:39:13:3913"}
{"level":"info","message":"AG-UI WebSocket connection closed: b21d08d0-e4e2-49fa-9974-28ff90242cc6","timestamp":"2025-06-04 00:39:13:3913"}
{"level":"info","message":"AG-UI WebSocket connection established: b6eeeaf2-0b3d-48bb-97d8-3b85b009f97b","timestamp":"2025-06-04 00:39:13:3913"}
{"level":"info","message":"AG-UI WebSocket connection established: 3fdbf1d3-5df3-4a94-9133-de121338a250","timestamp":"2025-06-04 00:39:13:3913"}
{"level":"info","message":"AG-UI WebSocket connection closed: b6eeeaf2-0b3d-48bb-97d8-3b85b009f97b","timestamp":"2025-06-04 00:39:13:3913"}
{"level":"info","message":"AG-UI WebSocket connection established: 4eb55bba-f1e1-4e60-bf32-3498f0ca9aa6","timestamp":"2025-06-04 00:39:15:3915"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3fdbf1d3-5df3-4a94-9133-de121338a250","timestamp":"2025-06-04 00:39:42:3942"}
{"level":"info","message":"AG-UI WebSocket connection closed: 4eb55bba-f1e1-4e60-bf32-3498f0ca9aa6","timestamp":"2025-06-04 00:39:42:3942"}
{"level":"info","message":"AG-UI WebSocket connection established: 3ea41127-1a19-4cfd-9986-eedcba8b8cd7","timestamp":"2025-06-04 00:39:42:3942"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3ea41127-1a19-4cfd-9986-eedcba8b8cd7","timestamp":"2025-06-04 00:39:42:3942"}
{"level":"info","message":"AG-UI WebSocket connection established: 1793ae8a-841e-41c6-89d0-7509db725697","timestamp":"2025-06-04 00:39:42:3942"}
{"level":"info","message":"AG-UI WebSocket connection established: d2835a3b-637d-43eb-8d6d-177f618f8fb2","timestamp":"2025-06-04 00:39:44:3944"}
{"level":"info","message":"AG-UI WebSocket connection closed: 1793ae8a-841e-41c6-89d0-7509db725697","timestamp":"2025-06-04 00:40:01:401"}
{"level":"info","message":"AG-UI WebSocket connection closed: d2835a3b-637d-43eb-8d6d-177f618f8fb2","timestamp":"2025-06-04 00:40:01:401"}
{"level":"info","message":"AG-UI WebSocket connection established: 949c0a63-3c0e-4991-b21d-c4100787427b","timestamp":"2025-06-04 00:40:01:401"}
{"level":"info","message":"AG-UI WebSocket connection closed: 949c0a63-3c0e-4991-b21d-c4100787427b","timestamp":"2025-06-04 00:40:01:401"}
{"level":"info","message":"AG-UI WebSocket connection established: 822fc8f3-b278-40ed-9754-df9f90c9b471","timestamp":"2025-06-04 00:40:01:401"}
{"level":"info","message":"AG-UI WebSocket connection established: 45058b93-6133-4a4c-b9a9-b8c9f689d859","timestamp":"2025-06-04 00:40:03:403"}
{"level":"info","message":"AG-UI WebSocket connection closed: 822fc8f3-b278-40ed-9754-df9f90c9b471","timestamp":"2025-06-04 00:40:09:409"}
{"level":"info","message":"AG-UI WebSocket connection closed: 45058b93-6133-4a4c-b9a9-b8c9f689d859","timestamp":"2025-06-04 00:40:09:409"}
{"level":"info","message":"AG-UI WebSocket connection established: d0ce3018-178b-47f2-90ac-d9913b9cc37f","timestamp":"2025-06-04 00:40:10:4010"}
{"level":"info","message":"AG-UI WebSocket connection established: c74ab7ea-6dae-4cb8-a9e4-30ffc963138b","timestamp":"2025-06-04 00:40:10:4010"}
{"level":"info","message":"AG-UI WebSocket connection closed: d0ce3018-178b-47f2-90ac-d9913b9cc37f","timestamp":"2025-06-04 00:40:10:4010"}
{"level":"info","message":"AG-UI WebSocket connection established: b2a14b59-b502-4c0f-92c0-c361eed4e345","timestamp":"2025-06-04 00:40:11:4011"}
{"level":"info","message":"AG-UI WebSocket connection closed: c74ab7ea-6dae-4cb8-a9e4-30ffc963138b","timestamp":"2025-06-04 00:40:44:4044"}
{"level":"info","message":"AG-UI WebSocket connection closed: b2a14b59-b502-4c0f-92c0-c361eed4e345","timestamp":"2025-06-04 00:40:44:4044"}
{"level":"info","message":"AG-UI WebSocket connection established: 3fa4a2ec-ce32-40df-805f-56c21d5d3b02","timestamp":"2025-06-04 00:40:44:4044"}
{"level":"info","message":"AG-UI WebSocket connection established: 1a18fb85-7caa-489b-9dae-bdfe6946448b","timestamp":"2025-06-04 00:40:44:4044"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3fa4a2ec-ce32-40df-805f-56c21d5d3b02","timestamp":"2025-06-04 00:40:44:4044"}
{"level":"info","message":"AG-UI WebSocket connection established: c2310741-9155-4624-8fde-71ecdb74443d","timestamp":"2025-06-04 00:40:46:4046"}
{"level":"info","message":"AG-UI WebSocket connection closed: 1a18fb85-7caa-489b-9dae-bdfe6946448b","timestamp":"2025-06-04 00:41:45:4145"}
{"level":"info","message":"AG-UI WebSocket connection closed: c2310741-9155-4624-8fde-71ecdb74443d","timestamp":"2025-06-04 00:41:45:4145"}
{"level":"info","message":"AG-UI WebSocket connection established: 06227175-7a8a-48cd-b1c0-9f6055a29336","timestamp":"2025-06-04 00:41:46:4146"}
{"level":"info","message":"AG-UI WebSocket connection established: 04b73b8c-01ea-4d75-8047-a0d3d00db6f8","timestamp":"2025-06-04 00:41:46:4146"}
{"level":"info","message":"AG-UI WebSocket connection closed: 06227175-7a8a-48cd-b1c0-9f6055a29336","timestamp":"2025-06-04 00:41:46:4146"}
{"level":"info","message":"AG-UI WebSocket connection established: b99efe6e-745b-4ac0-b37d-73ddc6988a99","timestamp":"2025-06-04 00:41:48:4148"}
{"level":"info","message":"AG-UI WebSocket connection closed: 04b73b8c-01ea-4d75-8047-a0d3d00db6f8","timestamp":"2025-06-04 00:41:48:4148"}
{"level":"info","message":"AG-UI WebSocket connection closed: b99efe6e-745b-4ac0-b37d-73ddc6988a99","timestamp":"2025-06-04 00:41:48:4148"}
{"level":"info","message":"AG-UI WebSocket connection established: 6e20c9e7-632c-4aec-8c73-2b13f563d730","timestamp":"2025-06-04 00:41:49:4149"}
{"level":"info","message":"AG-UI WebSocket connection established: c8df7075-c16c-44ba-8d12-8479b523460e","timestamp":"2025-06-04 00:41:49:4149"}
{"level":"info","message":"AG-UI WebSocket connection closed: 6e20c9e7-632c-4aec-8c73-2b13f563d730","timestamp":"2025-06-04 00:41:49:4149"}
{"level":"info","message":"AG-UI WebSocket connection established: 71b73f43-d196-4151-b874-31f6beee7051","timestamp":"2025-06-04 00:41:51:4151"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:55:38:5538"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:55:45:5545"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:45:5545"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:55:45:5545"}
{"level":"info","message":"AG-UI WebSocket connection established: 5baad3f4-63ac-4b53-8a4e-f5e900594798","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"AG-UI WebSocket connection established: 2d7f5490-90f7-46ea-9bca-a1221bb0cb14","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"AG-UI WebSocket connection established: b6baba6e-4a7e-43a3-bb62-946ffe82f6f6","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"AG-UI WebSocket connection established: e60ad8e7-bb7b-47b0-aeb1-30fbea220e41","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"AG-UI WebSocket connection established: b41e3a7d-9c2b-4b52-85fb-ab26af00fb2d","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"AG-UI WebSocket connection established: 594e7be8-92ee-4519-a5c6-8959838238cd","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"AG-UI WebSocket connection established: 144e42a0-72ea-4516-a96b-4166b3ace441","timestamp":"2025-06-04 00:55:46:5546"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:55:49:5549"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:55:55:5555"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:55:5555"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:55:55:5555"}
{"level":"info","message":"AG-UI WebSocket connection established: 70a4eb28-2ec5-4bbc-b372-a8638ee5e23e","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"AG-UI WebSocket connection established: 89d4e11a-6e0a-4dc2-9757-f1b1a63383f2","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"AG-UI WebSocket connection established: a74ea0d7-d2a8-4288-9b44-a9d59ae7a252","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"AG-UI WebSocket connection established: 6888b8bb-9287-4198-9b2c-5938c307e1e8","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"AG-UI WebSocket connection established: e6210050-edfd-45b3-851e-c4255405c542","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"AG-UI WebSocket connection established: 868f3139-11ab-44ff-9d73-7284dee10eec","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"AG-UI WebSocket connection established: 91c630ee-5b76-430d-9d42-b08b4154c61b","timestamp":"2025-06-04 00:55:56:5556"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:56:01:561"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:56:07:567"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:56:07:567"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:56:07:567"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:56:07:567"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:56:08:568"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:56:08:568"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:56:08:568"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: d9feb67b-4c2e-49fa-950b-63e2fd2053d0","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: 7468c306-b832-4cea-9410-910992f313c0","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: 2e063c3e-e186-47b1-b485-1dca3047f9e1","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: 244bb8f5-5c80-48bc-8ac8-86e2b0787877","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: 05598e49-39ad-4678-9e5b-a2a3a71d5881","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: d700615c-a5d4-41c9-86f8-02e57ced98ec","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"AG-UI WebSocket connection established: e4a2936c-7949-4234-85b6-5b74a274e7a3","timestamp":"2025-06-04 00:56:08:568"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 01:20:52:2052"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 01:20:58:2058"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:20:58:2058"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 01:20:58:2058"}
{"level":"info","message":"AG-UI WebSocket connection established: 4a28bfcb-bc88-42eb-80a6-867dafc24775","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"AG-UI WebSocket connection established: da46ffe0-d661-4769-99b0-94fa73023396","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"AG-UI WebSocket connection established: 78bd037f-cc12-4aa3-b5ad-86880593b196","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"AG-UI WebSocket connection established: 6a12d075-b937-4354-83d8-e4de60713e26","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"AG-UI WebSocket connection established: e4b34bdb-9a11-4ce8-8a66-d673cebc138a","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"AG-UI WebSocket connection established: 28a69f20-cadc-40f1-b9f6-419ea83146cf","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"AG-UI WebSocket connection established: c3a388f0-790b-4c57-9b06-6033d8dfd3dd","timestamp":"2025-06-04 01:20:59:2059"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 01:21:08:218"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 01:21:14:2114"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:21:14:2114"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 01:21:14:2114"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: 71ec8108-043e-440b-b330-e0df1b1241df","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: 1f82fc73-4b46-498e-907e-60e8aa9c649c","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: 11b8fbf8-0efa-4b40-9a4a-8c9897ffe47b","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: 51946783-971a-440f-8fe7-9350ac2c143a","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: b9f96ba9-36cc-4e8a-ada5-b019229298e8","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: 53740ba6-6f10-4e24-8486-36c23d095f16","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection established: 85f02cd2-b63c-4a56-bb95-0b1e7d294be2","timestamp":"2025-06-04 01:21:15:2115"}
{"level":"info","message":"AG-UI WebSocket connection closed: 53740ba6-6f10-4e24-8486-36c23d095f16","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection closed: 85f02cd2-b63c-4a56-bb95-0b1e7d294be2","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection closed: 11b8fbf8-0efa-4b40-9a4a-8c9897ffe47b","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection closed: 71ec8108-043e-440b-b330-e0df1b1241df","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection closed: 1f82fc73-4b46-498e-907e-60e8aa9c649c","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection closed: 51946783-971a-440f-8fe7-9350ac2c143a","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection closed: b9f96ba9-36cc-4e8a-ada5-b019229298e8","timestamp":"2025-06-04 01:29:52:2952"}
{"level":"info","message":"AG-UI WebSocket connection established: 01161499-d1a4-4b65-80c1-fa6f90f257f1","timestamp":"2025-06-04 08:08:21:821"}
{"level":"info","message":"AG-UI WebSocket connection established: c4e6241f-2129-4d39-9286-276daeac8942","timestamp":"2025-06-04 08:08:21:821"}
{"level":"info","message":"AG-UI WebSocket connection established: e2a97841-e97f-4f6b-ad3a-4d08989903ce","timestamp":"2025-06-04 08:08:21:821"}
{"level":"info","message":"AG-UI WebSocket connection established: 547242c2-a591-45eb-ac3b-4953e5f6073b","timestamp":"2025-06-04 08:08:21:821"}
{"level":"info","message":"AG-UI WebSocket connection established: a8a9f50c-76eb-4132-bfe1-107e176bc5de","timestamp":"2025-06-04 08:08:21:821"}
{"level":"info","message":"AG-UI WebSocket connection established: c956488b-581b-45d9-9b84-d86e6b37a379","timestamp":"2025-06-04 08:08:21:821"}
{"level":"info","message":"AG-UI WebSocket connection established: 60584698-70f5-4823-b5ab-978507860c1e","timestamp":"2025-06-04 08:08:21:821"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-06-04 08:08:24:824"}
{"error":"Socket closed unexpectedly","level":"error","message":"Redis client error","stack":"Error: Socket closed unexpectedly\n    at Socket.<anonymous> (/home/<USER>/Suplementor/backend/node_modules/@redis/client/dist/lib/client/socket.js:194:118)\n    at Object.onceWrapper (node:events:639:26)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 08:08:25:825"}
{"error":"read ECONNRESET","level":"error","message":"Redis client error","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:218:20)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"read ECONNRESET","level":"error","message":"Redis client error","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:218:20)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 08:08:25:825"}
{"error":"Socket closed unexpectedly","level":"error","message":"Redis client error","stack":"Error: Socket closed unexpectedly\n    at Socket.<anonymous> (/home/<USER>/Suplementor/backend/node_modules/@redis/client/dist/lib/client/socket.js:194:118)\n    at Object.onceWrapper (node:events:639:26)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"Socket closed unexpectedly","level":"error","message":"Redis client error","stack":"Error: Socket closed unexpectedly\n    at Socket.<anonymous> (/home/<USER>/Suplementor/backend/node_modules/@redis/client/dist/lib/client/socket.js:194:118)\n    at Object.onceWrapper (node:events:639:26)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:26:826"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:26:826"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:26:826"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:26:826"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:26:826"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:26:826"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:27:827"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:27:827"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:27:827"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:27:827"}
{"level":"info","message":"Redis client reconnecting...","timestamp":"2025-06-04 08:08:28:828"}
{"level":"error","message":"Redis reconnection failed after 10 attempts","timestamp":"2025-06-04 08:08:28:828"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:28:828"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 08:12:48:1248"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 08:12:54:1254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 08:12:54:1254"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 08:12:54:1254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 08:13:22:1322"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 08:13:22:1322"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 08:13:22:1322"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 08:44:35:4435"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 08:44:35:4435"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 08:44:35:4435"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:47:16:4716"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:47:16:4716"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:49:36:4936"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:49:36:4936"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:49:36:4936"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:49:36:4936"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:49:38:4938"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:38:4938"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:49:38:4938"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:49:38:4938"}
{"error":"connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"❌ Failed to connect to MongoDB","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1096:11)\n    at async MongoDBConnection.connect (/home/<USER>/Suplementor/backend/src/config/mongodb.ts:16:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:169:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:42:4942"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:51:59:5159"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:51:59:5159"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:51:59:5159"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:51:59:5159"}
{"error":"connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"❌ Failed to connect to MongoDB","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1096:11)\n    at async MongoDBConnection.connect (/home/<USER>/Suplementor/backend/src/config/mongodb.ts:16:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:169:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:04:524"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:52:20:5220"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:20:5220"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:52:20:5220"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:52:20:5220"}
{"error":"connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"❌ Failed to connect to MongoDB","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1096:11)\n    at async MongoDBConnection.connect (/home/<USER>/Suplementor/backend/src/config/mongodb.ts:16:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:169:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:25:5225"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:53:15:5315"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:312:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:15:5315"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:312:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:312:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:53:15:5315"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 09:53:16:5316"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 09:53:49:5349"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:53:50:5350"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:305:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:50:5350"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:305:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:305:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 09:53:50:5350"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 09:54:17:5417"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:54:18:5418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:298:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:18:5418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:298:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:298:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 09:54:18:5418"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 09:54:30:5430"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 09:54:32:5432"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:291:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:32:5432"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:291:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:291:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 11:10:38:1038"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 11:10:38:1038"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 11:10:39:1039"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 11:10:39:1039"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 11:10:40:1040"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 11:10:40:1040"}
{"contentLength":"104","duration":1,"ip":"::1","level":"info","message":"API: GET /health - 200 (1ms)","method":"GET","statusCode":200,"timestamp":"2025-06-04 11:12:09:129","url":"/health","userAgent":"curl/8.5.0"}
{"level":"info","message":"AG-UI WebSocket connection established: f2332a1c-70bb-4bd7-bb9e-28560dce9b5b","timestamp":"2025-06-04 11:19:23:1923"}
{"contentLength":"2007","duration":42,"ip":"::1","level":"warn","message":"API: POST /api/crawl/single - 404 (42ms)","method":"POST","statusCode":404,"timestamp":"2025-06-04 11:37:27:3727","url":"/api/crawl/single","userAgent":"curl/8.5.0"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 12:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 12:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 12:04:34:434"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 12:04:34:434"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 12:04:35:435"}
{"level":"info","message":"AG-UI WebSocket connection established: e27e8afb-fdd0-4ded-b963-384823e600ff","timestamp":"2025-06-04 17:07:09:79"}
{"level":"info","message":"AG-UI WebSocket connection closed: e27e8afb-fdd0-4ded-b963-384823e600ff","timestamp":"2025-06-04 17:07:10:710"}
{"level":"info","message":"AG-UI WebSocket connection established: 02285c65-a76a-4a52-ad32-d5c2f9af417b","timestamp":"2025-06-04 17:07:10:710"}
{"level":"info","message":"AG-UI WebSocket connection established: 1d00630e-d19b-4de9-8b36-b11027ff7f6c","timestamp":"2025-06-04 17:07:10:710"}
{"level":"info","message":"AG-UI WebSocket connection established: 966fedda-31de-45da-b5e4-a47bdf61009e","timestamp":"2025-06-04 17:07:30:730"}
{"level":"info","message":"AG-UI WebSocket connection closed: 966fedda-31de-45da-b5e4-a47bdf61009e","timestamp":"2025-06-04 17:07:30:730"}
{"level":"info","message":"AG-UI WebSocket connection established: df9ca0d5-0133-45bc-a577-7a0f5e69f9f9","timestamp":"2025-06-04 17:07:30:730"}
{"level":"info","message":"AG-UI WebSocket connection established: 9583adbe-b35e-4691-8235-b474a72eafbc","timestamp":"2025-06-04 17:07:31:731"}
{"level":"info","message":"AG-UI WebSocket connection closed: 9583adbe-b35e-4691-8235-b474a72eafbc","timestamp":"2025-06-04 17:07:31:731"}
{"level":"info","message":"AG-UI WebSocket connection established: 69cb1182-b388-4800-812d-0d341e69f4c7","timestamp":"2025-06-04 17:07:32:732"}
{"level":"info","message":"AG-UI WebSocket connection closed: 69cb1182-b388-4800-812d-0d341e69f4c7","timestamp":"2025-06-04 17:07:32:732"}
{"level":"info","message":"AG-UI WebSocket connection established: 7069ecf7-2e4c-42c2-bec2-deb288f3a9f0","timestamp":"2025-06-04 17:07:32:732"}
{"level":"info","message":"AG-UI WebSocket connection established: f0f91035-aa67-4917-929a-da746138a58d","timestamp":"2025-06-04 17:07:33:733"}
{"level":"info","message":"AG-UI WebSocket connection closed: f0f91035-aa67-4917-929a-da746138a58d","timestamp":"2025-06-04 17:07:33:733"}
{"level":"info","message":"Starting Gemma analysis for supplement: cytykolina","timestamp":"2025-06-04 17:07:36:736"}
{"level":"warn","message":"Empty or undefined response from Gemma for cytykolina, using fallback","timestamp":"2025-06-04 17:07:36:736"}
{"level":"info","message":"Gemma analysis completed for cytykolina","timestamp":"2025-06-04 17:07:36:736"}
{"duration":9546,"level":"info","message":"Graph: Search nodes (9546ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 17:07:45:745"}
{"duration":1267,"level":"info","message":"Graph: Create node (1 nodes) (1267ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":45,"level":"info","message":"Graph: Search nodes (45ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":44,"level":"info","message":"Graph: Create node (1 nodes) (44ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":452,"level":"info","message":"Graph: Create relationship (1 relationships) (452ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":45,"level":"info","message":"Graph: Search nodes (45ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":44,"level":"info","message":"Graph: Create node (1 nodes) (44ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":209,"level":"info","message":"Graph: Create relationship (1 relationships) (209ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:47:747"}
{"duration":74,"level":"info","message":"Graph: Create node (1 nodes) (74ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":109,"level":"info","message":"Graph: Create relationship (1 relationships) (109ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":24,"level":"info","message":"Graph: Create node (1 nodes) (24ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":11,"level":"info","message":"Graph: Create node (1 nodes) (11ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":12,"level":"info","message":"Graph: Create relationship (1 relationships) (12ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":12,"level":"info","message":"Graph: Create node (1 nodes) (12ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":243,"level":"info","message":"Graph: Create relationship (1 relationships) (243ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":120,"level":"info","message":"Graph: Create node (1 nodes) (120ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":17,"level":"info","message":"Graph: Create relationship (1 relationships) (17ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 17:07:48:748"}
{"duration":12294,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (12294ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 17:07:48:748"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 17:07:48:748"}
{"level":"info","message":"Agent completed for 1d00630e-d19b-4de9-8b36-b11027ff7f6c-530c8cd1-b82d-44e3-a6f5-37d62401e037","timestamp":"2025-06-04 17:07:48:748"}
{"level":"info","message":"AG-UI WebSocket connection established: 42e525c7-6e8a-48d3-8bf4-a145e581c664","timestamp":"2025-06-04 17:09:19:919"}
{"level":"info","message":"AG-UI WebSocket connection closed: 42e525c7-6e8a-48d3-8bf4-a145e581c664","timestamp":"2025-06-04 17:09:19:919"}
{"level":"info","message":"AG-UI WebSocket connection established: b193cfb5-1105-44af-bffd-30ced4ddf8da","timestamp":"2025-06-04 17:09:19:919"}
{"level":"info","message":"AG-UI WebSocket connection established: 8fc3ff39-26ed-44c1-93a9-b3fd91c91814","timestamp":"2025-06-04 17:09:20:920"}
{"level":"info","message":"AG-UI WebSocket connection closed: 8fc3ff39-26ed-44c1-93a9-b3fd91c91814","timestamp":"2025-06-04 17:09:23:923"}
