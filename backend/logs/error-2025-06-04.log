{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"error","message":"Error parsing Gemma analysis response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseAnalysisResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:265:27)\n    at GemmaService.analyzeSupplementData (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:74:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:108:24)","timestamp":"2025-06-04 00:02:12:212"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 609d873e-3e30-4b79-b681-5e6be82a00bb-2dabf81a-c7b1-4de3-b72d-c48fa94c258e: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:03:56:356"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:04:07:47"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:04:33:433"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:48:448"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:49:449"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:49:449"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:04:49:449"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:04:49:449"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 23abbf4c-2c40-4fe8-aa1e-5a6805863047-4ae2a3d6-50b6-470b-b7cc-8f3432fb10ca: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:04:49:449"}
{"error":"this.processParameters is not a function","level":"error","message":"Neo4j query failed","parameters":{},"query":"CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE","stack":"TypeError: this.processParameters is not a function\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:65:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Neo4jConnection.initializeSchema (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:140:11)\n    at async Neo4jConnection.connect (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:31:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:153:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:17:517"}
{"error":"this.processParameters is not a function","level":"error","message":"Neo4j query failed","parameters":{},"query":"CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE","stack":"TypeError: this.processParameters is not a function\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:65:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Neo4jConnection.initializeSchema (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:140:11)\n    at async Neo4jConnection.connect (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:31:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:153:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:17:517"}
{"error":"this.processParameters is not a function","level":"error","message":"Neo4j query failed","parameters":{},"query":"CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE","stack":"TypeError: this.processParameters is not a function\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:65:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Neo4jConnection.initializeSchema (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:140:11)\n    at async Neo4jConnection.connect (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:31:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:153:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:17:517"}
{"error":"this.processParameters is not a function","level":"error","message":"Neo4j query failed","parameters":{},"query":"CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE","stack":"TypeError: this.processParameters is not a function\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:65:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Neo4jConnection.initializeSchema (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:140:11)\n    at async Neo4jConnection.connect (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:31:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:153:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:21:521"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:05:44:544"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:05:44:544"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*tyrozyna*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"tyrozyna"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"tyrozyna","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:22:622"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for d06f8408-94b5-419f-8639-20ea31d37149-6792fd4d-eb4b-4082-ba19-40e222d018f0: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*tyrozyna*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"tyrozyna"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"tyrozyna","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:24:624"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 32902675-7b7b-4239-9b58-9f83dd0e5eb5-e92a63b9-2948-422f-96e4-2f4882224230: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:24:624"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:09:31:931"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:13:22:1322"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:26:09:269"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:45:5545"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:45:5545"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:55:5555"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:55:55:5555"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:56:08:568"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:56:08:568"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:20:58:2058"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:20:58:2058"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:21:14:2114"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 01:21:14:2114"}
{"error":"Socket closed unexpectedly","level":"error","message":"Redis client error","stack":"Error: Socket closed unexpectedly\n    at Socket.<anonymous> (/home/<USER>/Suplementor/backend/node_modules/@redis/client/dist/lib/client/socket.js:194:118)\n    at Object.onceWrapper (node:events:639:26)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"read ECONNRESET","level":"error","message":"Redis client error","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:218:20)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"read ECONNRESET","level":"error","message":"Redis client error","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:218:20)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"Socket closed unexpectedly","level":"error","message":"Redis client error","stack":"Error: Socket closed unexpectedly\n    at Socket.<anonymous> (/home/<USER>/Suplementor/backend/node_modules/@redis/client/dist/lib/client/socket.js:194:118)\n    at Object.onceWrapper (node:events:639:26)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"Socket closed unexpectedly","level":"error","message":"Redis client error","stack":"Error: Socket closed unexpectedly\n    at Socket.<anonymous> (/home/<USER>/Suplementor/backend/node_modules/@redis/client/dist/lib/client/socket.js:194:118)\n    at Object.onceWrapper (node:events:639:26)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:25:825"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:26:826"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:26:826"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:26:826"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:27:827"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:27:827"}
{"level":"error","message":"Redis reconnection failed after 10 attempts","timestamp":"2025-06-04 08:08:28:828"}
{"error":"connect ECONNREFUSED 127.0.0.1:6379","level":"error","message":"Redis client error","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","timestamp":"2025-06-04 08:08:28:828"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 08:12:54:1254"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 08:13:22:1322"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 08:44:35:4435"}
{"error":"Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687","level":"error","message":"❌ Failed to connect to Neo4j","stack":"Neo4jError: Failed to connect to server. Please ensure that your database is listening on the correct host and port and that you have compatible encryption settings both on Neo4j server and driver. Note that the default encryption setting has changed in Neo4j 4.0. Caused by: connect ECONNREFUSED 127.0.0.1:7687\n    at Neo4jError.GQLError [as constructor] (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:117:24)\n    at new Neo4jError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:204:28)\n    at newError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/error.js:246:12)\n    at NodeChannel._handleConnectionError (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-bolt-connection/lib/channel/node/node-channel.js:239:56)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-04 09:47:16:4716"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:38:4938"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:38:4938"}
{"error":"connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"❌ Failed to connect to MongoDB","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1096:11)\n    at async MongoDBConnection.connect (/home/<USER>/Suplementor/backend/src/config/mongodb.ts:16:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:169:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:49:42:4942"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:51:59:5159"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:51:59:5159"}
{"error":"connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"❌ Failed to connect to MongoDB","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1096:11)\n    at async MongoDBConnection.connect (/home/<USER>/Suplementor/backend/src/config/mongodb.ts:16:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:169:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:04:524"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:20:5220"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:20:5220"}
{"error":"connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"❌ Failed to connect to MongoDB","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/home/<USER>/Suplementor/backend/node_modules/mongoose/lib/connection.js:1096:11)\n    at async MongoDBConnection.connect (/home/<USER>/Suplementor/backend/src/config/mongodb.ts:16:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:169:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:52:25:5225"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:312:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:15:5315"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:312:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:15:5315"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:305:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:50:5350"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:305:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:53:50:5350"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:298:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:18:5418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:298:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:18:5418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:291:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:32:5432"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:291:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 09:54:32:5432"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 17:07:48:748"}
