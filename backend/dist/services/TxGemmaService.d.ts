interface TherapeuticProperty {
    name: string;
    category: string;
    mechanism: string;
    strength: 'low' | 'medium' | 'high';
    evidence: string;
    confidence: number;
}
interface RelationshipPrediction {
    sourceId: string;
    targetId: string;
    relationshipType: string;
    confidence: number;
    mechanism: string;
    therapeuticBasis: string;
    safetyProfile: {
        riskLevel: 'low' | 'medium' | 'high';
        contraindications: string[];
        warnings: string[];
        interactions: string[];
    };
    evidenceLevel: 'limited' | 'moderate' | 'strong';
    clinicalRelevance: number;
}
interface SafetyAnalysis {
    supplementId: string;
    overallRiskScore: number;
    riskFactors: {
        category: string;
        risk: string;
        severity: 'low' | 'medium' | 'high';
        evidence: string;
    }[];
    contraindications: {
        condition: string;
        severity: 'absolute' | 'relative';
        reason: string;
    }[];
    drugInteractions: {
        drug: string;
        interactionType: string;
        severity: 'minor' | 'moderate' | 'major';
        mechanism: string;
    }[];
    dosageRecommendations: {
        population: string;
        minDose: number;
        maxDose: number;
        unit: string;
        frequency: string;
        duration: string;
    }[];
}
export declare class TxGemmaService {
    private client;
    private baseUrl;
    private apiKey;
    constructor();
    analyzeTherapeuticProperties(supplementName: string, description?: string): Promise<TherapeuticProperty[]>;
    predictRelationships(sourceSupplementId: string, targetSupplementIds: string[], context?: {
        indication?: string;
        population?: string;
    }): Promise<RelationshipPrediction[]>;
    analyzeSafety(supplementId: string, supplementData: any): Promise<SafetyAnalysis>;
    batchPredictRelationships(supplementIds: string[]): Promise<RelationshipPrediction[]>;
    validatePredictions(predictions: RelationshipPrediction[]): Promise<any>;
    private getFallbackTherapeuticProperties;
    private getFallbackSafetyAnalysis;
}
export {};
//# sourceMappingURL=TxGemmaService.d.ts.map