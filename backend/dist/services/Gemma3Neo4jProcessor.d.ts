export declare class Gemma3Neo4jProcessor {
    private gemmaEndpoint;
    private maxRetries;
    private retryDelay;
    constructor(gemmaEndpoint?: string);
    processTextToGraph(text: string, sourceId?: string): Promise<any>;
    private processWithGemma3;
    private extractEntities;
    private extractRelationships;
    private analyzeSafety;
    private callGemma3;
    private createNeo4jGraph;
    private createEntities;
    private createRelationships;
    private createSafetyProfiles;
    private createSourceDocument;
    private parseEntitiesResponse;
    private parseRelationshipsResponse;
    private parseSafetyResponse;
    private fallbackEntityParsing;
    private getDefaultSafetyProfile;
    private calculateOverallConfidence;
}
//# sourceMappingURL=Gemma3Neo4jProcessor.d.ts.map