export declare class DataMigrationService {
    migrateSupplementData(dataSource: string, format: 'csv' | 'json'): Promise<any>;
    migrateStudyData(dataSource: string): Promise<any>;
    createRelationships(relationshipData: any[]): Promise<any>;
    validateDataIntegrity(): Promise<any>;
    private parseCsvFile;
    private parseJsonFile;
    private processBatch;
}
//# sourceMappingURL=DataMigrationService.d.ts.map