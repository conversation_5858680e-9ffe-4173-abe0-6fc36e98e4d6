"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TxGemmaService = void 0;
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const redis_1 = require("@/config/redis");
const axios_1 = __importDefault(require("axios"));
class TxGemmaService {
    client;
    baseUrl;
    apiKey;
    constructor() {
        this.baseUrl = environment_1.config.txgemma?.baseUrl || 'https://api.txgemma.com/v1';
        this.apiKey = environment_1.config.txgemma?.apiKey || '';
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'Suplementor-KnowledgeGraph/1.0'
            }
        });
        this.client.interceptors.request.use((config) => {
            logger_1.logger.info('TxGemma API Request', {
                url: config.url,
                method: config.method,
                dataSize: config.data ? JSON.stringify(config.data).length : 0
            });
            return config;
        }, (error) => {
            (0, logger_1.logError)('TxGemma API Request Error', error);
            return Promise.reject(error);
        });
        this.client.interceptors.response.use((response) => {
            logger_1.logger.info('TxGemma API Response', {
                status: response.status,
                dataSize: JSON.stringify(response.data).length
            });
            return response;
        }, (error) => {
            (0, logger_1.logError)('TxGemma API Response Error', error, {
                status: error.response?.status,
                statusText: error.response?.statusText
            });
            return Promise.reject(error);
        });
    }
    async analyzeTherapeuticProperties(supplementName, description) {
        const cacheKey = `txgemma:therapeutic:${supplementName}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const response = await this.client.post('/analyze/therapeutic-properties', {
                supplement: {
                    name: supplementName,
                    description: description || '',
                },
                analysis_depth: 'comprehensive',
                include_mechanisms: true,
                include_evidence: true
            });
            const properties = response.data.properties.map((prop) => ({
                name: prop.name,
                category: prop.category,
                mechanism: prop.mechanism_of_action,
                strength: prop.therapeutic_strength,
                evidence: prop.evidence_summary,
                confidence: prop.confidence_score
            }));
            await (0, redis_1.cacheSet)(cacheKey, properties, 86400);
            return properties;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to analyze therapeutic properties', error, { supplementName });
            return this.getFallbackTherapeuticProperties(supplementName);
        }
    }
    async predictRelationships(sourceSupplementId, targetSupplementIds, context) {
        const cacheKey = `txgemma:relationships:${sourceSupplementId}:${targetSupplementIds.join(',')}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const response = await this.client.post('/predict/relationships', {
                source_supplement: sourceSupplementId,
                target_supplements: targetSupplementIds,
                context: context || {},
                prediction_types: [
                    'synergistic',
                    'antagonistic',
                    'additive',
                    'competitive',
                    'contraindicated'
                ],
                include_safety_analysis: true,
                include_mechanisms: true
            });
            const predictions = response.data.predictions.map((pred) => ({
                sourceId: pred.source_id,
                targetId: pred.target_id,
                relationshipType: pred.relationship_type,
                confidence: pred.confidence_score,
                mechanism: pred.mechanism_description,
                therapeuticBasis: pred.therapeutic_rationale,
                safetyProfile: {
                    riskLevel: pred.safety_analysis.risk_level,
                    contraindications: pred.safety_analysis.contraindications || [],
                    warnings: pred.safety_analysis.warnings || [],
                    interactions: pred.safety_analysis.interactions || []
                },
                evidenceLevel: pred.evidence_level,
                clinicalRelevance: pred.clinical_relevance_score
            }));
            await (0, redis_1.cacheSet)(cacheKey, predictions, 43200);
            return predictions;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to predict relationships', error, { sourceSupplementId, targetSupplementIds });
            return [];
        }
    }
    async analyzeSafety(supplementId, supplementData) {
        const cacheKey = `txgemma:safety:${supplementId}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const response = await this.client.post('/analyze/safety', {
                supplement: {
                    id: supplementId,
                    name: supplementData.name,
                    ingredients: supplementData.ingredients || [],
                    dosage_range: supplementData.dosageRange,
                    description: supplementData.description
                },
                analysis_scope: 'comprehensive',
                include_drug_interactions: true,
                include_contraindications: true,
                include_population_specific: true
            });
            const safetyAnalysis = {
                supplementId,
                overallRiskScore: response.data.overall_risk_score,
                riskFactors: response.data.risk_factors.map((rf) => ({
                    category: rf.category,
                    risk: rf.description,
                    severity: rf.severity_level,
                    evidence: rf.evidence_summary
                })),
                contraindications: response.data.contraindications.map((ci) => ({
                    condition: ci.condition,
                    severity: ci.severity,
                    reason: ci.rationale
                })),
                drugInteractions: response.data.drug_interactions.map((di) => ({
                    drug: di.drug_name,
                    interactionType: di.interaction_type,
                    severity: di.severity,
                    mechanism: di.mechanism
                })),
                dosageRecommendations: response.data.dosage_recommendations.map((dr) => ({
                    population: dr.target_population,
                    minDose: dr.minimum_dose,
                    maxDose: dr.maximum_dose,
                    unit: dr.dose_unit,
                    frequency: dr.frequency,
                    duration: dr.duration
                }))
            };
            await (0, redis_1.cacheSet)(cacheKey, safetyAnalysis, 86400);
            return safetyAnalysis;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to analyze safety', error, { supplementId });
            return this.getFallbackSafetyAnalysis(supplementId);
        }
    }
    async batchPredictRelationships(supplementIds) {
        const batchSize = 10;
        const allPredictions = [];
        for (let i = 0; i < supplementIds.length; i += batchSize) {
            const batch = supplementIds.slice(i, i + batchSize);
            try {
                const batchPromises = batch.map(sourceId => this.predictRelationships(sourceId, supplementIds.filter(id => id !== sourceId)));
                const batchResults = await Promise.all(batchPromises);
                allPredictions.push(...batchResults.flat());
                if (i + batchSize < supplementIds.length) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            catch (error) {
                (0, logger_1.logError)('Batch prediction failed', error, { batchStart: i, batchSize: batch.length });
            }
        }
        return allPredictions;
    }
    async validatePredictions(predictions) {
        try {
            const response = await this.client.post('/validate/predictions', {
                predictions: predictions.map(pred => ({
                    source_id: pred.sourceId,
                    target_id: pred.targetId,
                    relationship_type: pred.relationshipType,
                    confidence: pred.confidence
                })),
                validation_criteria: {
                    minimum_confidence: 0.7,
                    require_evidence: true,
                    check_contradictions: true
                }
            });
            return {
                validatedPredictions: response.data.validated_predictions,
                rejectedPredictions: response.data.rejected_predictions,
                validationSummary: response.data.validation_summary
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to validate predictions', error);
            return {
                validatedPredictions: predictions.filter(p => p.confidence >= 0.7),
                rejectedPredictions: predictions.filter(p => p.confidence < 0.7),
                validationSummary: { total: predictions.length, validated: 0, rejected: 0 }
            };
        }
    }
    getFallbackTherapeuticProperties(supplementName) {
        const commonProperties = {
            'vitamin-c': [{
                    name: 'Antioxidant Activity',
                    category: 'Antioxidant',
                    mechanism: 'Free radical scavenging',
                    strength: 'high',
                    evidence: 'Well-established',
                    confidence: 0.9
                }],
            'omega-3': [{
                    name: 'Anti-inflammatory',
                    category: 'Inflammation',
                    mechanism: 'Prostaglandin modulation',
                    strength: 'high',
                    evidence: 'Strong clinical evidence',
                    confidence: 0.85
                }]
        };
        return commonProperties[supplementName.toLowerCase()] || [];
    }
    getFallbackSafetyAnalysis(supplementId) {
        return {
            supplementId,
            overallRiskScore: 0.3,
            riskFactors: [],
            contraindications: [],
            drugInteractions: [],
            dosageRecommendations: []
        };
    }
}
exports.TxGemmaService = TxGemmaService;
//# sourceMappingURL=TxGemmaService.js.map