# 🤖 Algorytmy AI - Suplementor Platform

## 📋 Spis Treści

1. [Przegląd AI Pipeline](#przegląd-ai-pipeline)
2. [Gemma3 Medical Integration](#gemma3-medical-integration)
3. [Knowledge Graph AI](#knowledge-graph-ai)
4. [Recommendation Engine](#recommendation-engine)
5. [Interaction Prediction](#interaction-prediction)
6. [NLP Pipeline](#nlp-pipeline)
7. [Model Training & Evaluation](#model-training--evaluation)
8. [Real-time Inference](#real-time-inference)

---

## 🎯 Przegląd AI Pipeline

### AI Architecture Overview

```mermaid
graph TB
    subgraph "Data Ingestion"
        PUBMED[PubMed Articles]
        FDA[FDA Database]
        USER_DATA[User Interactions]
        CLINICAL[Clinical Trials]
    end
    
    subgraph "NLP Pipeline"
        EXTRACT[Entity Extraction]
        CLASSIFY[Text Classification]
        SENTIMENT[Sentiment Analysis]
        SUMMARIZE[Summarization]
    end
    
    subgraph "Knowledge Graph AI"
        EMBED[Graph Embeddings]
        LINK[Link Prediction]
        CLUSTER[Community Detection]
        REASON[Graph Reasoning]
    end
    
    subgraph "ML Models"
        INTERACTION[Interaction Predictor]
        RECOMMEND[Recommendation Engine]
        SAFETY[Safety Classifier]
        DOSAGE[Dosage Optimizer]
    end
    
    subgraph "Gemma3 Integration"
        MEDICAL[Medical Analysis]
        QA[Question Answering]
        EXPLAIN[Explanation Generation]
    end
    
    PUBMED --> EXTRACT
    FDA --> CLASSIFY
    USER_DATA --> SENTIMENT
    CLINICAL --> SUMMARIZE
    
    EXTRACT --> EMBED
    CLASSIFY --> LINK
    SENTIMENT --> CLUSTER
    SUMMARIZE --> REASON
    
    EMBED --> INTERACTION
    LINK --> RECOMMEND
    CLUSTER --> SAFETY
    REASON --> DOSAGE
    
    INTERACTION --> MEDICAL
    RECOMMEND --> QA
    SAFETY --> EXPLAIN
```

### Core AI Components

1. **🧠 Gemma3 Medical AI**: Large language model for medical analysis
2. **📊 Graph Neural Networks**: Knowledge graph reasoning
3. **🔍 NLP Pipeline**: Text processing and entity extraction
4. **🎯 Recommendation System**: Personalized supplement suggestions
5. **⚠️ Safety Predictor**: Interaction and side effect prediction
6. **📈 Optimization Engine**: Dosage and cost optimization

---

## 🏥 Gemma3 Medical Integration

### Model Configuration

```typescript
interface Gemma3Config {
  model: 'gemma3-medical-7b' | 'gemma3-medical-27b'
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
}

class Gemma3Service {
  private config: Gemma3Config = {
    model: 'gemma3-medical-7b',
    temperature: 0.1, // Low for medical accuracy
    maxTokens: 2048,
    topP: 0.9,
    frequencyPenalty: 0.0,
    presencePenalty: 0.0
  }

  async analyzeMedicalQuery(
    query: MedicalQuery,
    context: MedicalContext
  ): Promise<MedicalAnalysis> {
    const prompt = this.buildMedicalPrompt(query, context)
    
    const response = await this.client.generate({
      ...this.config,
      prompt,
      systemPrompt: this.getMedicalSystemPrompt()
    })
    
    return this.parseMedicalResponse(response)
  }

  private getMedicalSystemPrompt(): string {
    return `
      You are a medical AI assistant specialized in supplement analysis.
      
      Guidelines:
      1. Always prioritize patient safety
      2. Base recommendations on peer-reviewed evidence
      3. Clearly state confidence levels and limitations
      4. Recommend consulting healthcare providers for serious conditions
      5. Use evidence-based medicine principles
      6. Consider drug-supplement interactions
      7. Account for individual patient factors
      
      Response format:
      - Safety Assessment: [High/Medium/Low risk]
      - Evidence Level: [Strong/Moderate/Limited/Insufficient]
      - Recommendations: [Specific actionable advice]
      - Warnings: [Any safety concerns]
      - References: [Supporting evidence]
    `
  }

  private buildMedicalPrompt(
    query: MedicalQuery,
    context: MedicalContext
  ): string {
    return `
      Patient Context:
      - Age: ${context.patient.age}
      - Gender: ${context.patient.gender}
      - Medical History: ${context.patient.conditions.join(', ')}
      - Current Medications: ${context.patient.medications.join(', ')}
      - Allergies: ${context.patient.allergies.join(', ')}
      
      Supplement Query:
      ${query.supplements.map(s => `
        - ${s.name}
          Dosage: ${s.dosage}
          Duration: ${s.duration}
          Purpose: ${s.purpose}
      `).join('\n')}
      
      Specific Question: ${query.question}
      
      Please provide a comprehensive medical analysis including:
      1. Safety assessment for this specific patient
      2. Potential interactions with current medications
      3. Dosage recommendations
      4. Monitoring suggestions
      5. Alternative options if applicable
      6. When to consult a healthcare provider
    `
  }
}
```

### Medical Analysis Pipeline

```typescript
interface MedicalAnalysis {
  safetyAssessment: {
    overallRisk: 'low' | 'medium' | 'high'
    riskFactors: string[]
    contraindications: string[]
  }
  interactions: {
    drugSupplementInteractions: Interaction[]
    supplementSupplementInteractions: Interaction[]
    foodInteractions: string[]
  }
  dosageRecommendations: {
    supplement: string
    recommendedDose: string
    maxSafeDose: string
    timing: string
    withFood: boolean
  }[]
  monitoring: {
    labTests: string[]
    symptoms: string[]
    frequency: string
  }
  evidenceLevel: 'strong' | 'moderate' | 'limited' | 'insufficient'
  confidence: number // 0-1
  references: string[]
}

class MedicalAnalysisEngine {
  async performComprehensiveAnalysis(
    supplements: Supplement[],
    userProfile: HealthProfile
  ): Promise<MedicalAnalysis> {
    // Step 1: Gemma3 medical analysis
    const gemmaAnalysis = await this.gemma3Service.analyzeMedicalQuery({
      supplements,
      question: 'Comprehensive safety and efficacy analysis'
    }, {
      patient: userProfile
    })
    
    // Step 2: Drug interaction database lookup
    const drugInteractions = await this.checkDrugInteractions(
      supplements,
      userProfile.medications
    )
    
    // Step 3: Supplement-supplement interaction prediction
    const supplementInteractions = await this.predictSupplementInteractions(
      supplements
    )
    
    // Step 4: Dosage optimization
    const dosageRecommendations = await this.optimizeDosages(
      supplements,
      userProfile
    )
    
    // Step 5: Evidence synthesis
    const evidenceAnalysis = await this.synthesizeEvidence(supplements)
    
    return this.combineAnalyses({
      gemmaAnalysis,
      drugInteractions,
      supplementInteractions,
      dosageRecommendations,
      evidenceAnalysis
    })
  }
}
```

---

## 🕸️ Knowledge Graph AI

### Graph Neural Network Architecture

```python
import torch
import torch.nn as nn
from torch_geometric.nn import GCNConv, GATConv, GraphSAGE

class SupplementGraphNN(nn.Module):
    def __init__(self, num_features, hidden_dim, num_classes):
        super().__init__()
        self.num_features = num_features
        self.hidden_dim = hidden_dim
        self.num_classes = num_classes
        
        # Graph Attention Network layers
        self.gat1 = GATConv(num_features, hidden_dim, heads=8, dropout=0.1)
        self.gat2 = GATConv(hidden_dim * 8, hidden_dim, heads=1, dropout=0.1)
        
        # Graph Convolutional layers
        self.gcn1 = GCNConv(hidden_dim, hidden_dim)
        self.gcn2 = GCNConv(hidden_dim, hidden_dim)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
    def forward(self, x, edge_index, batch=None):
        # Graph Attention
        x = F.relu(self.gat1(x, edge_index))
        x = F.dropout(x, training=self.training)
        x = self.gat2(x, edge_index)
        
        # Graph Convolution
        x = F.relu(self.gcn1(x, edge_index))
        x = F.dropout(x, training=self.training)
        x = self.gcn2(x, edge_index)
        
        # Global pooling for graph-level prediction
        if batch is not None:
            x = global_mean_pool(x, batch)
        
        # Classification
        return self.classifier(x)

# Graph Embedding Generation
class GraphEmbeddingService:
    def __init__(self, model_path: str):
        self.model = torch.load(model_path)
        self.model.eval()
    
    def generate_embeddings(self, graph_data):
        with torch.no_grad():
            embeddings = self.model.encode(graph_data)
        return embeddings.numpy()
    
    def find_similar_supplements(
        self, 
        target_supplement: str, 
        top_k: int = 10
    ) -> List[Tuple[str, float]]:
        target_embedding = self.get_supplement_embedding(target_supplement)
        
        # Cosine similarity search
        similarities = cosine_similarity(
            target_embedding.reshape(1, -1),
            self.all_embeddings
        )[0]
        
        # Get top-k similar supplements
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        return [
            (self.supplement_names[idx], similarities[idx])
            for idx in top_indices
        ]
```

### Link Prediction for Interactions

```python
class InteractionPredictor(nn.Module):
    def __init__(self, embedding_dim):
        super().__init__()
        self.embedding_dim = embedding_dim
        
        # Interaction prediction network
        self.interaction_net = nn.Sequential(
            nn.Linear(embedding_dim * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # No interaction, Mild, Severe
        )
        
    def forward(self, supplement1_emb, supplement2_emb):
        # Concatenate embeddings
        combined = torch.cat([supplement1_emb, supplement2_emb], dim=-1)
        
        # Predict interaction probability
        interaction_logits = self.interaction_net(combined)
        
        return F.softmax(interaction_logits, dim=-1)

# Training Pipeline
class InteractionTrainer:
    def __init__(self, model, train_loader, val_loader):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        self.criterion = nn.CrossEntropyLoss()
        
    def train_epoch(self):
        self.model.train()
        total_loss = 0
        
        for batch in self.train_loader:
            self.optimizer.zero_grad()
            
            supp1_emb = batch['supplement1_embedding']
            supp2_emb = batch['supplement2_embedding']
            labels = batch['interaction_label']
            
            predictions = self.model(supp1_emb, supp2_emb)
            loss = self.criterion(predictions, labels)
            
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            
        return total_loss / len(self.train_loader)
    
    def evaluate(self):
        self.model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch in self.val_loader:
                supp1_emb = batch['supplement1_embedding']
                supp2_emb = batch['supplement2_embedding']
                labels = batch['interaction_label']
                
                predictions = self.model(supp1_emb, supp2_emb)
                _, predicted = torch.max(predictions.data, 1)
                
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
                
        return correct / total
```

---

## 🎯 Recommendation Engine

### Hybrid Recommendation System

```typescript
interface RecommendationEngine {
  collaborativeFiltering: CollaborativeFilter
  contentBasedFilter: ContentBasedFilter
  knowledgeBasedFilter: KnowledgeBasedFilter
  hybridCombiner: HybridCombiner
}

class CollaborativeFilter {
  async findSimilarUsers(
    targetUser: User,
    minSimilarity: number = 0.7
  ): Promise<User[]> {
    // Matrix factorization approach
    const userItemMatrix = await this.buildUserItemMatrix()
    const userEmbeddings = await this.factorizeMatrix(userItemMatrix)
    
    const targetEmbedding = userEmbeddings[targetUser.id]
    const similarities = this.calculateCosineSimilarities(
      targetEmbedding,
      userEmbeddings
    )
    
    return similarities
      .filter(sim => sim.score >= minSimilarity)
      .map(sim => sim.user)
  }

  async recommendBasedOnSimilarUsers(
    targetUser: User,
    similarUsers: User[]
  ): Promise<Recommendation[]> {
    const supplementCounts = new Map<string, number>()
    const supplementRatings = new Map<string, number[]>()
    
    // Aggregate supplements from similar users
    for (const user of similarUsers) {
      const userSupplements = await this.getUserSupplements(user.id)
      
      for (const supplement of userSupplements) {
        const count = supplementCounts.get(supplement.id) || 0
        supplementCounts.set(supplement.id, count + 1)
        
        const ratings = supplementRatings.get(supplement.id) || []
        ratings.push(supplement.userRating)
        supplementRatings.set(supplement.id, ratings)
      }
    }
    
    // Calculate recommendation scores
    const recommendations: Recommendation[] = []
    
    for (const [supplementId, count] of supplementCounts) {
      const ratings = supplementRatings.get(supplementId)!
      const avgRating = ratings.reduce((a, b) => a + b) / ratings.length
      const popularity = count / similarUsers.length
      
      const score = avgRating * popularity
      
      recommendations.push({
        supplementId,
        score,
        reason: `${count} similar users rated this ${avgRating.toFixed(1)}/5`,
        type: 'collaborative'
      })
    }
    
    return recommendations.sort((a, b) => b.score - a.score)
  }
}

class ContentBasedFilter {
  async recommendBasedOnProfile(
    userProfile: HealthProfile
  ): Promise<Recommendation[]> {
    // Extract user preferences and health goals
    const userVector = this.createUserVector(userProfile)
    
    // Get all supplements and their feature vectors
    const supplements = await this.getAllSupplements()
    const supplementVectors = supplements.map(s => 
      this.createSupplementVector(s)
    )
    
    // Calculate similarities
    const similarities = supplementVectors.map((vector, index) => ({
      supplement: supplements[index],
      similarity: this.cosineSimilarity(userVector, vector)
    }))
    
    // Filter and rank
    return similarities
      .filter(sim => sim.similarity > 0.5)
      .sort((a, b) => b.similarity - a.similarity)
      .map(sim => ({
        supplementId: sim.supplement.id,
        score: sim.similarity,
        reason: this.explainSimilarity(userProfile, sim.supplement),
        type: 'content-based'
      }))
  }

  private createUserVector(profile: HealthProfile): number[] {
    const vector: number[] = []
    
    // Demographics features
    vector.push(profile.demographics.age / 100) // Normalized age
    vector.push(profile.demographics.gender === 'male' ? 1 : 0)
    vector.push(profile.demographics.bmi / 50) // Normalized BMI
    
    // Health conditions (one-hot encoding)
    const allConditions = this.getAllHealthConditions()
    for (const condition of allConditions) {
      vector.push(
        profile.medicalHistory.conditions.includes(condition) ? 1 : 0
      )
    }
    
    // Health goals (one-hot encoding)
    const allGoals = this.getAllHealthGoals()
    for (const goal of allGoals) {
      vector.push(
        profile.goals.some(g => g.type === goal) ? 1 : 0
      )
    }
    
    // Lifestyle factors
    vector.push(this.encodeActivityLevel(profile.lifestyle.activityLevel))
    vector.push(this.encodeDiet(profile.lifestyle.diet))
    
    return vector
  }
}
```

### Multi-Objective Optimization

```python
from scipy.optimize import minimize
import numpy as np

class SupplementStackOptimizer:
    def __init__(self):
        self.objectives = {
            'health_benefit': self.calculate_health_benefit,
            'cost_effectiveness': self.calculate_cost_effectiveness,
            'safety_score': self.calculate_safety_score,
            'interaction_risk': self.calculate_interaction_risk
        }
        
    def optimize_stack(
        self, 
        candidate_supplements: List[Supplement],
        user_profile: HealthProfile,
        budget_constraint: float,
        max_supplements: int = 5
    ) -> OptimizedStack:
        
        # Define decision variables (binary: include supplement or not)
        n_supplements = len(candidate_supplements)
        
        def objective_function(x):
            selected_supplements = [
                supp for i, supp in enumerate(candidate_supplements) 
                if x[i] > 0.5
            ]
            
            if len(selected_supplements) == 0:
                return float('inf')
            
            # Multi-objective scoring
            health_score = self.calculate_health_benefit(
                selected_supplements, user_profile
            )
            cost_score = self.calculate_cost_effectiveness(
                selected_supplements, budget_constraint
            )
            safety_score = self.calculate_safety_score(selected_supplements)
            interaction_penalty = self.calculate_interaction_risk(
                selected_supplements
            )
            
            # Weighted combination (weights can be user-customizable)
            total_score = (
                0.4 * health_score +
                0.3 * cost_score +
                0.2 * safety_score -
                0.1 * interaction_penalty
            )
            
            return -total_score  # Minimize negative score
        
        def constraint_budget(x):
            total_cost = sum(
                candidate_supplements[i].price 
                for i in range(n_supplements) 
                if x[i] > 0.5
            )
            return budget_constraint - total_cost
        
        def constraint_max_supplements(x):
            return max_supplements - sum(x[i] > 0.5 for i in range(n_supplements))
        
        # Constraints
        constraints = [
            {'type': 'ineq', 'fun': constraint_budget},
            {'type': 'ineq', 'fun': constraint_max_supplements}
        ]
        
        # Bounds (binary variables)
        bounds = [(0, 1) for _ in range(n_supplements)]
        
        # Initial guess
        x0 = np.random.rand(n_supplements)
        
        # Optimize
        result = minimize(
            objective_function,
            x0,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints
        )
        
        # Extract selected supplements
        selected_indices = [
            i for i in range(n_supplements) 
            if result.x[i] > 0.5
        ]
        
        selected_supplements = [
            candidate_supplements[i] for i in selected_indices
        ]
        
        return OptimizedStack(
            supplements=selected_supplements,
            total_score=-result.fun,
            total_cost=sum(s.price for s in selected_supplements),
            optimization_details=result
        )
    
    def calculate_health_benefit(
        self, 
        supplements: List[Supplement], 
        user_profile: HealthProfile
    ) -> float:
        total_benefit = 0
        
        for supplement in supplements:
            # Calculate benefit based on user's health goals
            for goal in user_profile.goals:
                if goal.type in supplement.benefits:
                    benefit_strength = supplement.benefits[goal.type]
                    evidence_weight = supplement.evidence_levels[goal.type]
                    goal_priority = goal.priority
                    
                    benefit = benefit_strength * evidence_weight * goal_priority
                    total_benefit += benefit
        
        return total_benefit / len(supplements) if supplements else 0
```

---

*Dokument aktualizowany: 2025-01-30*
