# 🎨 Interfejs Użytkownika - Suplementor Platform

## 📋 Spis Treści

1. [Przegląd Designu](#przegląd-designu)
2. [Technologie Frontend](#technologie-frontend)
3. [Atomic Design System](#atomic-design-system)
4. [Główne Komponenty UI](#główne-komponenty-ui)
5. [Nawigacja i UX](#nawigacja-i-ux)
6. [Responsywność](#responsywność)
7. [Accessibility](#accessibility)
8. [Performance](#performance)

---

## 🎯 Przegląd Designu

### Design Philosophy
> "Elegancja w prostocie, moc w funkcjonalności"

Interfejs Suplementor został zaprojektowany z myślą o **profesjonalnych użytkownikach zdrowia** oraz **świadomych konsumentach**. Główne zasady designu:

- **🔬 Medical-Grade Precision**: Czytelność i precyzja informacji medycznych
- **🎨 Modern Aesthetics**: Współczesny, minimalistyczny design
- **⚡ Performance-First**: <PERSON><PERSON>b<PERSON>ś<PERSON> i responsywność na pierwszym miejscu
- **♿ Universal Access**: Dostępność dla wszystkich użytkowników

### Kolorystyka

```css
:root {
  /* Primary Colors - Medical Blue Palette */
  --primary-50: #eff6ff;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* Secondary Colors - Health Green */
  --secondary-50: #f0fdf4;
  --secondary-500: #22c55e;
  --secondary-600: #16a34a;
  
  /* Accent Colors - Warning & Error */
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* Neutral Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-900: #111827;
}
```

### Typografia

**Primary Font**: Inter (Google Fonts)
- **Headings**: Inter Bold (700)
- **Body Text**: Inter Regular (400)
- **Code/Data**: JetBrains Mono

**Hierarchy**:
- H1: 2.5rem (40px) - Page titles
- H2: 2rem (32px) - Section headers
- H3: 1.5rem (24px) - Subsections
- Body: 1rem (16px) - Main content
- Small: 0.875rem (14px) - Metadata

---

## 🛠️ Technologie Frontend

### Core Stack

```json
{
  "framework": "React 18.2.0",
  "language": "TypeScript 5.0",
  "bundler": "Vite 4.4",
  "styling": "Tailwind CSS 3.3",
  "components": "Headless UI 1.7",
  "visualization": "D3.js 7.8",
  "state": "React Query 4.29",
  "routing": "React Router 6.14"
}
```

### Kluczowe Biblioteki

```typescript
// UI Components
import { Dialog, Transition } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/24/outline'

// Data Visualization
import * as d3 from 'd3'
import { ForceGraph2D } from 'react-force-graph'

// Forms & Validation
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

// Animations
import { motion, AnimatePresence } from 'framer-motion'

// Charts
import { LineChart, BarChart } from 'recharts'
```

---

## 🧩 Atomic Design System

### Atoms (Podstawowe Elementy)

#### Button Component
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost'
  size: 'sm' | 'md' | 'lg'
  loading?: boolean
  disabled?: boolean
  children: React.ReactNode
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  ...props
}) => {
  const baseClasses = 'font-medium rounded-lg transition-all duration-200'
  const variants = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700',
    secondary: 'bg-secondary-600 text-white hover:bg-secondary-700',
    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-50',
    ghost: 'text-primary-600 hover:bg-primary-50'
  }
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }
  
  return (
    <button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]}`}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? <Spinner /> : children}
    </button>
  )
}
```

#### Input Component
```typescript
interface InputProps {
  label: string
  type: 'text' | 'email' | 'password' | 'number'
  placeholder?: string
  error?: string
  required?: boolean
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  required,
  ...props
}) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">
      {label} {required && <span className="text-error-500">*</span>}
    </label>
    <input
      className={`
        w-full px-3 py-2 border rounded-lg
        focus:ring-2 focus:ring-primary-500 focus:border-primary-500
        ${error ? 'border-error-500' : 'border-gray-300'}
      `}
      {...props}
    />
    {error && (
      <p className="text-sm text-error-500">{error}</p>
    )}
  </div>
)
```

### Molecules (Złożone Komponenty)

#### Search Bar
```typescript
const SearchBar: React.FC = () => {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState([])
  
  return (
    <div className="relative">
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
        <input
          type="text"
          placeholder="Szukaj suplementów, składników, efektów..."
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
      </div>
      
      {suggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className="px-4 py-2 hover:bg-gray-50 cursor-pointer"
            >
              {suggestion.name}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
```

#### Supplement Card
```typescript
interface SupplementCardProps {
  supplement: {
    id: string
    name: string
    description: string
    rating: number
    price: number
    effects: string[]
    interactions: number
  }
}

const SupplementCard: React.FC<SupplementCardProps> = ({ supplement }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    className="bg-white rounded-lg shadow-md p-6 border border-gray-200"
  >
    <div className="flex justify-between items-start mb-4">
      <h3 className="text-lg font-semibold text-gray-900">
        {supplement.name}
      </h3>
      <div className="flex items-center space-x-1">
        <StarIcon className="h-4 w-4 text-yellow-400" />
        <span className="text-sm text-gray-600">{supplement.rating}</span>
      </div>
    </div>
    
    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
      {supplement.description}
    </p>
    
    <div className="flex flex-wrap gap-2 mb-4">
      {supplement.effects.slice(0, 3).map((effect, index) => (
        <span
          key={index}
          className="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full"
        >
          {effect}
        </span>
      ))}
    </div>
    
    <div className="flex justify-between items-center">
      <span className="text-lg font-bold text-primary-600">
        ${supplement.price}
      </span>
      <Button size="sm">Dodaj do profilu</Button>
    </div>
    
    {supplement.interactions > 0 && (
      <div className="mt-3 p-2 bg-warning-50 border border-warning-200 rounded">
        <p className="text-xs text-warning-700">
          ⚠️ {supplement.interactions} potencjalnych interakcji
        </p>
      </div>
    )}
  </motion.div>
)
```

### Organisms (Kompleksowe Sekcje)

#### Dashboard Header
```typescript
const DashboardHeader: React.FC = () => {
  const { user } = useAuth()
  const { data: healthScore } = useHealthScore()
  
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Witaj, {user?.firstName}! 👋
            </h1>
            <p className="text-gray-600">
              Twój Health Score: 
              <span className="ml-2 font-semibold text-secondary-600">
                {healthScore}/100
              </span>
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <NotificationBell />
            <UserProfileDropdown />
          </div>
        </div>
      </div>
    </header>
  )
}
```

---

## 🧭 Nawigacja i UX

### Główna Nawigacja

```typescript
const MainNavigation: React.FC = () => {
  const location = useLocation()
  
  const navItems = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    { name: 'Mój Profil', href: '/profile', icon: UserIcon },
    { name: 'Graf Wiedzy', href: '/knowledge-graph', icon: ShareIcon },
    { name: 'Rekomendacje', href: '/recommendations', icon: LightBulbIcon },
    { name: 'Badania', href: '/research', icon: BeakerIcon },
    { name: 'Analityka', href: '/analytics', icon: ChartBarIcon }
  ]
  
  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex space-x-8">
          {navItems.map((item) => {
            const isActive = location.pathname === item.href
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`
                  flex items-center px-3 py-4 text-sm font-medium
                  border-b-2 transition-colors duration-200
                  ${isActive 
                    ? 'border-primary-500 text-primary-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                  }
                `}
              >
                <item.icon className="h-5 w-5 mr-2" />
                {item.name}
              </Link>
            )
          })}
        </div>
      </div>
    </nav>
  )
}
```

### Breadcrumbs
```typescript
const Breadcrumbs: React.FC<{ items: BreadcrumbItem[] }> = ({ items }) => (
  <nav className="flex mb-6" aria-label="Breadcrumb">
    <ol className="flex items-center space-x-2">
      {items.map((item, index) => (
        <li key={index} className="flex items-center">
          {index > 0 && (
            <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" />
          )}
          {item.href ? (
            <Link
              to={item.href}
              className="text-primary-600 hover:text-primary-700"
            >
              {item.name}
            </Link>
          ) : (
            <span className="text-gray-500">{item.name}</span>
          )}
        </li>
      ))}
    </ol>
  </nav>
)
```

---

## 📱 Responsywność

### Breakpoints
```css
/* Tailwind CSS Breakpoints */
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet portrait */
lg: 1024px  /* Tablet landscape / Small desktop */
xl: 1280px  /* Desktop */
2xl: 1536px /* Large desktop */
```

### Responsive Grid System
```typescript
const ResponsiveGrid: React.FC = ({ children }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {children}
  </div>
)
```

### Mobile-First Approach
```typescript
const MobileNavigation: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <div className="md:hidden">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-md text-gray-600"
      >
        <Bars3Icon className="h-6 w-6" />
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-16 left-0 right-0 bg-white shadow-lg"
          >
            {/* Mobile menu items */}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
```

---

## ♿ Accessibility

### WCAG 2.1 AA Compliance

```typescript
// Focus management
const FocusTrap: React.FC = ({ children }) => {
  const trapRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    const trap = createFocusTrap(trapRef.current!)
    trap.activate()
    return () => trap.deactivate()
  }, [])
  
  return <div ref={trapRef}>{children}</div>
}

// Screen reader support
const ScreenReaderOnly: React.FC = ({ children }) => (
  <span className="sr-only">{children}</span>
)

// ARIA labels and roles
const AccessibleButton: React.FC = ({ children, ...props }) => (
  <button
    role="button"
    aria-label="Dodaj suplement do profilu"
    {...props}
  >
    {children}
  </button>
)
```

### Keyboard Navigation
```typescript
const useKeyboardNavigation = () => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          // Close modals/dropdowns
          break
        case 'Tab':
          // Focus management
          break
        case 'Enter':
        case ' ':
          // Activate buttons
          break
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])
}
```

---

## ⚡ Performance

### Code Splitting
```typescript
// Lazy loading components
const KnowledgeGraph = lazy(() => import('./components/KnowledgeGraph'))
const Analytics = lazy(() => import('./pages/Analytics'))

// Route-based splitting
const AppRoutes = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <Routes>
      <Route path="/knowledge-graph" element={<KnowledgeGraph />} />
      <Route path="/analytics" element={<Analytics />} />
    </Routes>
  </Suspense>
)
```

### Image Optimization
```typescript
const OptimizedImage: React.FC<ImageProps> = ({ src, alt, ...props }) => (
  <img
    src={src}
    alt={alt}
    loading="lazy"
    decoding="async"
    {...props}
  />
)
```

### Performance Metrics
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

---

*Dokument aktualizowany: 2025-01-30*
