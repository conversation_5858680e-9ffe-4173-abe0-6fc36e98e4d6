# 👥 Scenariusze Użytkowania - Suplementor Platform

## 📋 Spis Treści

1. [Personas <PERSON>wnik<PERSON>](#personas-użytkowników)
2. [User Journey Maps](#user-journey-maps)
3. [Case Studies](#case-studies)
4. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#przykłady-interakcji)
5. [Edge Cases](#edge-cases)
6. [Accessibility Scenarios](#accessibility-scenarios)
7. [Mobile Use Cases](#mobile-use-cases)
8. [Healthcare Provider Integration](#healthcare-provider-integration)

---

## 👤 Personas Użytkowników

### 1. Anna - Świadoma Konsumentka (28 lat)

**Profil**:
- Marketing Manager w firmie tech
- Aktywny tryb życia, regularne treningi
- Budżet: 200-300 PLN/miesiąc na suplementy
- Cele: poprawa energii, wsparcie odporności, optymalizacja wydajności

**Potrzeby**:
- Szybki dostęp do sprawdzonych informacji
- Personalizowane rekomendacje
- Śledzenie postępów
- Integracja z aplikacjami fitness

**Frustracje**:
- Przeciążenie informacyjne w internecie
- Sprzeczne opinie ekspertów
- Wysokie ceny premium suplementów
- Brak jasnych wytycznych dawkowania

### 2. Dr. Kowalski - Lekarz Rodzinny (45 lat)

**Profil**:
- 15 lat doświadczenia w medycynie
- Prowadzi prywatną praktykę
- Zainteresowany medycyną integracyjną
- Potrzebuje narzędzi do konsultacji z pacjentami

**Potrzeby**:
- Dostęp do aktualnych badań naukowych
- Narzędzia do sprawdzania interakcji
- Możliwość tworzenia planów dla pacjentów
- Integracja z systemami medycznymi

**Frustracje**:
- Brak czasu na śledzenie najnowszych badań
- Pacjenci przynoszą informacje z niepewnych źródeł
- Trudność w ocenie jakości suplementów
- Brak standardów w branży suplementów

### 3. Marek - Senior z Problemami Zdrowotnymi (67 lat)

**Profil**:
- Emeryt, były nauczyciel
- Cukrzyca typu 2, nadciśnienie
- Przyjmuje 5 leków na stałe
- Ograniczony budżet: 100-150 PLN/miesiąc

**Potrzeby**:
- Prosty, intuicyjny interfejs
- Sprawdzanie interakcji z lekami
- Przypomnienia o dawkowaniu
- Wsparcie w języku polskim

**Frustracje**:
- Skomplikowane interfejsy
- Małe czcionki i słaba czytelność
- Obawy o bezpieczeństwo interakcji
- Wysokie ceny specjalistycznych suplementów

### 4. Kasia - Młoda Mama (32 lata)

**Profil**:
- Matka 2-letniego dziecka
- Karmi piersią
- Pracuje zdalnie jako grafik
- Budżet: 150-200 PLN/miesiąc

**Potrzeby**:
- Bezpieczne suplementy podczas karmienia
- Wsparcie energii i nastroju
- Szybkie wyszukiwanie informacji
- Mobilny dostęp do platformy

**Frustracje**:
- Brak czasu na długie researche
- Obawy o wpływ na dziecko
- Zmęczenie i brak energii
- Sprzeczne informacje o bezpieczeństwie

---

## 🗺️ User Journey Maps

### Journey 1: Anna - Pierwsza wizyta na platformie

```mermaid
journey
    title Anna's First Visit Journey
    section Discovery
      Searches for "best supplements for energy": 3: Anna
      Finds Suplementor through Google: 4: Anna
      Lands on homepage: 3: Anna
    section Exploration
      Reads about AI-powered recommendations: 4: Anna
      Checks testimonials and reviews: 4: Anna
      Starts free account creation: 5: Anna
    section Onboarding
      Completes health profile questionnaire: 4: Anna
      Receives personalized dashboard: 5: Anna
      Explores knowledge graph visualization: 5: Anna
    section First Recommendation
      Gets AI-generated supplement stack: 5: Anna
      Reviews safety analysis: 4: Anna
      Compares prices and options: 4: Anna
    section Decision
      Adds supplements to wishlist: 4: Anna
      Shares results with friend: 5: Anna
      Subscribes to premium plan: 5: Anna
```

### Journey 2: Dr. Kowalski - Konsultacja z pacjentem

```mermaid
journey
    title Dr. Kowalski's Patient Consultation
    section Preparation
      Reviews patient's medical history: 4: Dr. Kowalski
      Logs into Suplementor professional account: 5: Dr. Kowalski
      Searches for patient's conditions: 4: Dr. Kowalski
    section Analysis
      Inputs patient's medications: 4: Dr. Kowalski
      Reviews interaction warnings: 5: Dr. Kowalski
      Checks latest research updates: 5: Dr. Kowalski
    section Consultation
      Discusses options with patient: 5: Dr. Kowalski
      Shows evidence-based recommendations: 5: Dr. Kowalski
      Explains potential risks and benefits: 4: Dr. Kowalski
    section Follow-up
      Creates monitoring plan: 4: Dr. Kowalski
      Schedules follow-up reminders: 4: Dr. Kowalski
      Exports report for patient: 5: Dr. Kowalski
```

---

## 📚 Case Studies

### Case Study 1: Optymalizacja Suplementacji dla Sportowca

**Sytuacja**: 
Tomasz, 29-letni crossfitter, chce zoptymalizować swoją suplementację przed zawodami.

**Wyzwania**:
- Intensywne treningi 6x w tygodniu
- Potrzeba szybkiej regeneracji
- Ograniczony budżet (250 PLN/miesiąc)
- Unikanie substancji zakazanych w sporcie

**Proces w Suplementor**:

1. **Profil Zdrowotny**:
```json
{
  "demographics": {
    "age": 29,
    "gender": "male",
    "weight": 82,
    "height": 178,
    "bmi": 25.9,
    "activityLevel": "very_active"
  },
  "goals": [
    {
      "type": "muscle_recovery",
      "priority": "high",
      "timeline": "3_months"
    },
    {
      "type": "energy_performance",
      "priority": "high",
      "timeline": "immediate"
    },
    {
      "type": "joint_health",
      "priority": "medium",
      "timeline": "long_term"
    }
  ],
  "constraints": [
    "wada_compliant",
    "budget_250_pln",
    "no_stimulants_evening"
  ]
}
```

2. **AI Analiza i Rekomendacje**:
```typescript
const recommendations = await aiService.generateRecommendations({
  userProfile: tomaszProfile,
  constraints: {
    budget: 250,
    wadaCompliant: true,
    maxSupplements: 4
  }
})

// Wynik:
{
  recommendedStack: [
    {
      supplement: "Creatine Monohydrate",
      dosage: "5g daily",
      timing: "post-workout",
      cost: 45,
      evidenceLevel: "strong",
      safetyScore: 9.5
    },
    {
      supplement: "Whey Protein Isolate",
      dosage: "25g post-workout",
      timing: "within 30min post-exercise",
      cost: 120,
      evidenceLevel: "strong",
      safetyScore: 9.8
    },
    {
      supplement: "Omega-3 EPA/DHA",
      dosage: "2g daily",
      timing: "with meals",
      cost: 60,
      evidenceLevel: "moderate",
      safetyScore: 9.2
    },
    {
      supplement: "Magnesium Glycinate",
      dosage: "400mg evening",
      timing: "before bed",
      cost: 25,
      evidenceLevel: "moderate",
      safetyScore: 9.7
    }
  ],
  totalCost: 250,
  expectedBenefits: {
    recoveryImprovement: "15-25%",
    performanceGain: "8-12%",
    sleepQuality: "20-30%"
  },
  monitoringPlan: {
    trackMetrics: ["sleep_quality", "workout_performance", "recovery_time"],
    checkInFrequency: "weekly",
    adjustmentPeriod: "4_weeks"
  }
}
```

3. **Rezultaty po 3 miesiącach**:
- Poprawa czasu regeneracji o 22%
- Wzrost siły w kluczowych ćwiczeniach o 11%
- Lepsza jakość snu (8.2/10 vs 6.5/10)
- Brak skutków ubocznych
- Oszczędność 15% w porównaniu do poprzedniej suplementacji

### Case Study 2: Wsparcie Seniora z Wielochorobowością

**Sytuacja**: 
Maria, 72 lata, cukrzyca typu 2, nadciśnienie, osteoporoza, przyjmuje 6 leków.

**Wyzwania**:
- Liczne interakcje lek-suplement
- Ograniczona absorpcja niektórych składników
- Problemy z połykaniem dużych tabletek
- Budżet 120 PLN/miesiąc

**Proces w Suplementor**:

1. **Szczegółowa Analiza Interakcji**:
```typescript
const interactionAnalysis = await aiService.analyzeInteractions({
  medications: [
    "Metformin 1000mg 2x daily",
    "Amlodipine 5mg daily",
    "Atorvastatin 20mg evening",
    "Alendronate 70mg weekly",
    "Aspirin 75mg daily",
    "Pantoprazole 40mg daily"
  ],
  proposedSupplements: [
    "Vitamin D3",
    "Vitamin B12",
    "Calcium",
    "Magnesium"
  ]
})

// Wykryte interakcje:
{
  criticalInteractions: [
    {
      supplement: "Calcium",
      medication: "Alendronate",
      severity: "high",
      mechanism: "Reduced absorption of alendronate",
      recommendation: "Take calcium 2+ hours after alendronate"
    }
  ],
  moderateInteractions: [
    {
      supplement: "Magnesium",
      medication: "Amlodipine",
      severity: "moderate",
      mechanism: "Potential additive hypotensive effect",
      recommendation: "Monitor blood pressure, start with lower dose"
    }
  ],
  beneficialInteractions: [
    {
      supplement: "Vitamin B12",
      medication: "Metformin",
      effect: "Metformin may deplete B12",
      recommendation: "B12 supplementation recommended"
    }
  ]
}
```

2. **Personalizowany Plan**:
```json
{
  "optimizedStack": [
    {
      "supplement": "Vitamin D3 + K2",
      "form": "liquid_drops",
      "dosage": "2000 IU D3 + 100mcg K2",
      "timing": "morning_with_breakfast",
      "rationale": "Liquid form for better absorption, K2 for bone health"
    },
    {
      "supplement": "Methylcobalamin B12",
      "form": "sublingual_tablet",
      "dosage": "1000mcg daily",
      "timing": "morning",
      "rationale": "Sublingual for better absorption, addresses metformin depletion"
    },
    {
      "supplement": "Magnesium Bisglycinate",
      "form": "small_capsules",
      "dosage": "200mg evening",
      "timing": "2_hours_after_dinner",
      "rationale": "Chelated form, evening timing avoids BP medication interaction"
    }
  ],
  "avoidedSupplements": [
    {
      "supplement": "Calcium",
      "reason": "High interaction risk with alendronate, adequate dietary intake"
    }
  ],
  "monitoringProtocol": {
    "bloodPressure": "daily_morning",
    "bloodGlucose": "as_prescribed",
    "symptoms": ["dizziness", "nausea", "muscle_cramps"],
    "labTests": ["25(OH)D", "B12", "Mg"] 
  }
}
```

3. **Rezultaty po 6 miesiącach**:
- Poziom witaminy D wzrósł z 18 ng/ml do 35 ng/ml
- B12 w normie (wcześniej niedobór)
- Poprawa jakości snu
- Brak nowych interakcji
- Zadowolenie z prostoty dawkowania

---

## 🔄 Przykłady Interakcji

### Interakcja 1: Wyszukiwanie Suplementu

**User**: "Szukam czegoś na poprawę koncentracji"

**System Response**:
```json
{
  "searchResults": [
    {
      "supplement": "Lion's Mane Mushroom",
      "relevanceScore": 0.92,
      "benefits": ["cognitive_enhancement", "focus", "memory"],
      "evidenceLevel": "moderate",
      "safetyProfile": "excellent",
      "avgRating": 4.3,
      "priceRange": "45-80 PLN"
    },
    {
      "supplement": "Bacopa Monnieri",
      "relevanceScore": 0.89,
      "benefits": ["memory", "anxiety_reduction", "focus"],
      "evidenceLevel": "strong",
      "safetyProfile": "good",
      "avgRating": 4.1,
      "priceRange": "35-60 PLN"
    }
  ],
  "aiInsight": "Na podstawie Twojego profilu, Lion's Mane może być szczególnie korzystny ze względu na Twój styl życia i cele. Bacopa Monnieri ma silniejsze dowody naukowe, ale wymaga dłuższego stosowania (8-12 tygodni) dla pełnych efektów.",
  "personalizedNote": "Uwaga: Masz w profilu wrażliwość na kofeiny - oba suplementy są bezpieczne pod tym względem."
}
```

### Interakcja 2: Sprawdzanie Interakcji

**User**: Dodaje nowy suplement do swojego stacku

**System**: Automatyczna analiza interakcji
```typescript
const interactionCheck = {
  newSupplement: "Ashwagandha 600mg",
  currentStack: ["Magnesium", "Vitamin D", "Omega-3"],
  currentMedications: ["Levothyroxine"],
  
  analysis: {
    interactions: [
      {
        type: "supplement-medication",
        severity: "moderate",
        description: "Ashwagandha może wpływać na funkcję tarczycy",
        recommendation: "Monitoruj TSH co 6-8 tygodni, skonsultuj z endokrynologiem",
        sources: ["PubMed:12345678", "Cochrane:CD009876"]
      }
    ],
    safetyScore: 7.2,
    recommendedAction: "proceed_with_caution",
    monitoringRequired: true
  }
}
```

### Interakcja 3: AI Chat Consultation

**User**: "Czy mogę łączyć magnez z wapniem?"

**AI Response**:
```
🤖 **Suplementor AI**: 

Tak, możesz łączyć magnez z wapniem, ale jest kilka ważnych zasad:

**✅ Zalecenia:**
• Stosunek Ca:Mg powinien wynosić 2:1 lub 1:1
• Najlepiej przyjmować w różnych porach dnia
• Magnez wieczorem (wspomaga sen)
• Wapń rano lub w południe

**⚠️ Uwagi:**
• Duże dawki wapnia (>500mg) mogą zmniejszać wchłanianie magnezu
• Twój profil pokazuje niedobór magnezu - priorytet dla Mg

**📊 Dla Twojego profilu:**
• Magnez: 400mg wieczorem (bisglycynian)
• Wapń: 500mg rano (cytrynian) - jeśli potrzebny

**🔬 Dowody naukowe:** Silne (15 badań klinicznych)

Czy chcesz, żebym przeanalizował Twoje obecne dawki?
```

---

## ⚠️ Edge Cases

### Edge Case 1: Ciąża i Karmienie

**Scenariusz**: Użytkowniczka w 2. trymestrze ciąży

**Wyzwania**:
- Ograniczona lista bezpiecznych suplementów
- Zmieniające się potrzeby w trakcie ciąży
- Interakcje z witaminami prenatalymi

**Rozwiązanie Systemu**:
```typescript
const pregnancyProtocol = {
  allowedSupplements: [
    "Folic Acid", "Iron", "Vitamin D", "Omega-3 DHA", "Probiotics"
  ],
  restrictedSupplements: [
    "Vitamin A (>3000 IU)", "Herbs", "High-dose vitamins"
  ],
  trimesterSpecific: {
    second: {
      focus: ["Iron deficiency prevention", "Fetal development"],
      monitoring: ["Hemoglobin", "Ferritin"]
    }
  },
  automaticAdjustments: true,
  consultationRequired: true
}
```

### Edge Case 2: Rzadka Choroba Genetyczna

**Scenariusz**: Użytkownik z fenyloketonurią (PKU)

**Wyzwania**:
- Ograniczenia dietetyczne
- Specjalne potrzeby suplementacyjne
- Brak standardowych protokołów

**Rozwiązanie**:
```typescript
const rareConditionProtocol = {
  condition: "phenylketonuria",
  restrictions: {
    phenylalanine: "strict_avoidance",
    protein: "medical_formula_only"
  },
  requiredSupplements: [
    "Tyrosine", "Iron", "Vitamin B12", "Zinc"
  ],
  specialistConsultation: "required",
  monitoringFrequency: "monthly"
}
```

---

## ♿ Accessibility Scenarios

### Scenariusz 1: Użytkownik z Dysleksją

**Adaptacje**:
- Opcja czytania tekstu przez syntezator mowy
- Uproszczone instrukcje
- Wizualne wskaźniki postępu
- Możliwość zmiany czcionki na dyslexia-friendly

### Scenariusz 2: Użytkownik z Problemami Wzroku

**Adaptacje**:
- Wysokie kontrasty kolorów
- Skalowalne czcionki (do 200%)
- Kompatybilność ze screen readerami
- Alternatywne opisy dla grafik

### Scenariusz 3: Użytkownik z Ograniczeniami Motorycznymi

**Adaptacje**:
- Nawigacja klawiaturą
- Większe obszary klikalne
- Opcje głosowe
- Uproszczone gesty na mobile

---

## 📱 Mobile Use Cases

### Use Case 1: Szybkie Sprawdzenie w Aptece

**Scenariusz**: Anna stoi w aptece i chce sprawdzić suplement

**Funkcjonalności**:
- Skanowanie kodu kreskowego
- Szybka analiza składników
- Porównanie z profilem użytkownika
- Alternatywne propozycje

### Use Case 2: Przypomnienie o Dawce

**Scenariusz**: Powiadomienie push o przyjęciu suplementu

**Funkcjonalności**:
- Smart notifications oparte na czasie posiłków
- Możliwość przełożenia na później
- Tracking adherence
- Motywacyjne komunikaty

---

## 🏥 Healthcare Provider Integration

### Scenariusz 1: Telemedycyna

**Integracja**:
- Udostępnianie raportu lekarzowi
- Real-time collaboration podczas konsultacji
- Bezpieczna wymiana danych medycznych
- Integracja z systemami EMR

### Scenariusz 2: Apteka Kliniczna

**Funkcjonalności**:
- Weryfikacja recept vs suplementy
- Edukacja pacjentów
- Monitoring adherence
- Raportowanie do lekarza prowadzącego

---

*Dokument aktualizowany: 2025-01-30*
