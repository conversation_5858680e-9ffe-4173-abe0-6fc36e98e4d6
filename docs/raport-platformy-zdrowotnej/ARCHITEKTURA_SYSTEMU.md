# 🏗️ Architektura Systemu - Suplementor Platform

## 📋 Spis Treści

1. [Przegląd Architektury](#przegląd-architektury)
2. [Architektura Mikroserwisów](#architektura-mikroserwisów)
3. [<PERSON><PERSON>](#bazy-danych)
4. [API Gateway i Routing](#api-gateway-i-routing)
5. [Caching Strategy](#caching-strategy)
6. [Security Architecture](#security-architecture)
7. [Monitoring i Observability](#monitoring-i-observability)
8. [Deployment i DevOps](#deployment-i-devops)

---

## 🎯 Przegląd Architektury

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web App - React]
        MOBILE[Mobile App - React Native]
        API_DOCS[API Documentation]
    end
    
    subgraph "API Gateway"
        GATEWAY[Kong API Gateway]
        AUTH[Authentication Service]
        RATE[Rate Limiting]
    end
    
    subgraph "Microservices"
        USER[User Service]
        SUPPLEMENT[Supplement Service]
        AI[AI/ML Service]
        RESEARCH[Research Service]
        NOTIFICATION[Notification Service]
    end
    
    subgraph "Data Layer"
        NEO4J[(Neo4j - Knowledge Graph)]
        MONGO[(MongoDB - User Data)]
        REDIS[(Redis - Cache)]
        ELASTIC[(Elasticsearch - Search)]
    end
    
    subgraph "External Services"
        PUBMED[PubMed API]
        FDA[FDA Database]
        GEMMA[Gemma3 AI]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    GATEWAY --> USER
    GATEWAY --> SUPPLEMENT
    GATEWAY --> AI
    GATEWAY --> RESEARCH
    GATEWAY --> NOTIFICATION
    
    USER --> MONGO
    SUPPLEMENT --> NEO4J
    AI --> GEMMA
    RESEARCH --> PUBMED
    RESEARCH --> FDA
    
    USER --> REDIS
    SUPPLEMENT --> REDIS
    AI --> REDIS
```

### Kluczowe Zasady Architektury

1. **🔄 Microservices Architecture**: Niezależne, skalowalne serwisy
2. **📊 Event-Driven Design**: Asynchroniczna komunikacja przez eventy
3. **🔒 Security-First**: Bezpieczeństwo na każdym poziomie
4. **⚡ Performance-Optimized**: Cache-first approach
5. **🔧 DevOps-Ready**: Containerization i CI/CD

---

## 🔧 Architektura Mikroserwisów

### User Service

```typescript
// User Service Architecture
interface UserService {
  // Core user management
  createUser(userData: CreateUserRequest): Promise<User>
  updateProfile(userId: string, profile: HealthProfile): Promise<User>
  getHealthScore(userId: string): Promise<HealthScore>
  
  // Health profile management
  addHealthCondition(userId: string, condition: HealthCondition): Promise<void>
  updateMedications(userId: string, medications: Medication[]): Promise<void>
  setHealthGoals(userId: string, goals: HealthGoal[]): Promise<void>
}

// Health Profile Schema
interface HealthProfile {
  demographics: {
    age: number
    gender: 'male' | 'female' | 'other'
    weight: number
    height: number
    bmi: number
  }
  medicalHistory: {
    conditions: HealthCondition[]
    medications: Medication[]
    allergies: string[]
    surgeries: Surgery[]
  }
  lifestyle: {
    activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active'
    diet: 'omnivore' | 'vegetarian' | 'vegan' | 'keto' | 'paleo'
    smoking: boolean
    alcohol: 'none' | 'light' | 'moderate' | 'heavy'
    sleep: {
      averageHours: number
      quality: 1 | 2 | 3 | 4 | 5
    }
  }
  goals: HealthGoal[]
  preferences: {
    budget: {
      min: number
      max: number
      currency: string
    }
    supplementTypes: string[]
    avoidIngredients: string[]
  }
}
```

### Supplement Service

```typescript
// Supplement Service with Neo4j Integration
class SupplementService {
  constructor(
    private neo4jService: Neo4jService,
    private cacheService: RedisService
  ) {}

  async searchSupplements(query: SearchQuery): Promise<Supplement[]> {
    const cacheKey = `search:${JSON.stringify(query)}`
    
    // Check cache first
    const cached = await this.cacheService.get(cacheKey)
    if (cached) return cached
    
    // Neo4j query for semantic search
    const cypherQuery = `
      MATCH (s:Supplement)-[:CONTAINS]->(i:Ingredient)
      WHERE s.name CONTAINS $query 
         OR i.name CONTAINS $query
         OR EXISTS {
           MATCH (s)-[:HAS_EFFECT]->(e:Effect)
           WHERE e.name CONTAINS $query
         }
      RETURN s, collect(i) as ingredients, 
             collect(e) as effects
      ORDER BY s.popularity DESC
      LIMIT $limit
    `
    
    const result = await this.neo4jService.executeQuery(cypherQuery, {
      query: query.term,
      limit: query.limit || 20
    })
    
    const supplements = this.mapNeo4jResults(result)
    
    // Cache results for 1 hour
    await this.cacheService.set(cacheKey, supplements, 3600)
    
    return supplements
  }

  async getSupplementInteractions(
    supplementIds: string[]
  ): Promise<Interaction[]> {
    const cypherQuery = `
      MATCH (s1:Supplement)-[:INTERACTS_WITH]-(s2:Supplement)
      WHERE s1.id IN $supplementIds 
        AND s2.id IN $supplementIds
      RETURN s1, s2, r.severity as severity, 
             r.mechanism as mechanism,
             r.evidence_level as evidenceLevel
    `
    
    const result = await this.neo4jService.executeQuery(cypherQuery, {
      supplementIds
    })
    
    return this.mapInteractionResults(result)
  }
}
```

### AI/ML Service

```typescript
// AI Service with Gemma3 Integration
class AIService {
  constructor(
    private gemma3Client: Gemma3Client,
    private mlPipeline: MLPipeline
  ) {}

  async analyzeSupplementStack(
    supplements: Supplement[],
    userProfile: HealthProfile
  ): Promise<AnalysisResult> {
    // Prepare data for Gemma3
    const context = this.prepareAnalysisContext(supplements, userProfile)
    
    // Call Gemma3 for medical analysis
    const medicalAnalysis = await this.gemma3Client.analyze({
      model: 'gemma3-medical',
      prompt: this.buildAnalysisPrompt(context),
      temperature: 0.1, // Low temperature for medical accuracy
      maxTokens: 1000
    })
    
    // Run interaction prediction model
    const interactionPrediction = await this.mlPipeline.predictInteractions({
      supplements: supplements.map(s => s.id),
      userFactors: this.extractUserFactors(userProfile)
    })
    
    // Combine results
    return {
      medicalAnalysis: medicalAnalysis.response,
      interactionRisk: interactionPrediction.riskScore,
      recommendations: this.generateRecommendations(
        medicalAnalysis,
        interactionPrediction,
        userProfile
      ),
      confidence: Math.min(
        medicalAnalysis.confidence,
        interactionPrediction.confidence
      )
    }
  }

  private buildAnalysisPrompt(context: AnalysisContext): string {
    return `
      Analyze the following supplement stack for a patient:
      
      Patient Profile:
      - Age: ${context.userProfile.demographics.age}
      - Gender: ${context.userProfile.demographics.gender}
      - Medical Conditions: ${context.userProfile.medicalHistory.conditions.join(', ')}
      - Current Medications: ${context.userProfile.medicalHistory.medications.join(', ')}
      
      Supplement Stack:
      ${context.supplements.map(s => `- ${s.name}: ${s.dosage}`).join('\n')}
      
      Please provide:
      1. Safety assessment
      2. Potential interactions
      3. Dosage recommendations
      4. Monitoring suggestions
      
      Focus on evidence-based analysis and highlight any concerns.
    `
  }
}
```

---

## 🗄️ Bazy Danych

### Neo4j - Knowledge Graph

```cypher
-- Core Node Types
CREATE CONSTRAINT supplement_id FOR (s:Supplement) REQUIRE s.id IS UNIQUE;
CREATE CONSTRAINT ingredient_id FOR (i:Ingredient) REQUIRE i.id IS UNIQUE;
CREATE CONSTRAINT effect_id FOR (e:Effect) REQUIRE e.id IS UNIQUE;
CREATE CONSTRAINT study_id FOR (st:Study) REQUIRE st.id IS UNIQUE;

-- Relationship Types
(:Supplement)-[:CONTAINS {amount: float, unit: string}]->(:Ingredient)
(:Supplement)-[:HAS_EFFECT {strength: float, evidence_level: int}]->(:Effect)
(:Supplement)-[:INTERACTS_WITH {severity: string, mechanism: string}]->(:Supplement)
(:Study)-[:INVESTIGATES]->(:Supplement)
(:Study)-[:SUPPORTS]->(:Effect)

-- Example Data Model
MERGE (magnesium:Supplement {
  id: 'mg-glycinate-001',
  name: 'Magnesium Glycinate',
  category: 'Mineral',
  bioavailability: 0.85,
  commonDosage: '200-400mg',
  safetyRating: 9.2
})

MERGE (sleep:Effect {
  id: 'sleep-quality',
  name: 'Sleep Quality Improvement',
  category: 'Sleep',
  mechanism: 'GABA receptor modulation'
})

MERGE (magnesium)-[:HAS_EFFECT {
  strength: 0.78,
  evidence_level: 4,
  studies_count: 23
}]->(sleep)
```

### MongoDB - User Data

```typescript
// User Collection Schema
interface UserDocument {
  _id: ObjectId
  email: string
  passwordHash: string
  profile: {
    firstName: string
    lastName: string
    dateOfBirth: Date
    createdAt: Date
    updatedAt: Date
  }
  healthProfile: HealthProfile
  supplementStack: {
    supplements: Array<{
      supplementId: string
      dosage: string
      frequency: string
      startDate: Date
      endDate?: Date
      notes?: string
    }>
    lastUpdated: Date
  }
  preferences: UserPreferences
  analytics: {
    healthScore: number
    lastCalculated: Date
    trends: Array<{
      metric: string
      value: number
      date: Date
    }>
  }
}

// Indexes for performance
db.users.createIndex({ email: 1 }, { unique: true })
db.users.createIndex({ "healthProfile.demographics.age": 1 })
db.users.createIndex({ "supplementStack.supplements.supplementId": 1 })
db.users.createIndex({ "analytics.healthScore": -1 })
```

### Redis - Caching Strategy

```typescript
// Cache Patterns
interface CacheStrategy {
  // User session cache
  userSessions: {
    key: `session:${userId}`
    ttl: 86400 // 24 hours
    data: UserSession
  }
  
  // Search results cache
  searchResults: {
    key: `search:${hashQuery(query)}`
    ttl: 3600 // 1 hour
    data: SearchResult[]
  }
  
  // AI analysis cache
  aiAnalysis: {
    key: `ai:${hashInput(supplements, profile)}`
    ttl: 7200 // 2 hours
    data: AnalysisResult
  }
  
  // Real-time data cache
  realtimeData: {
    key: `realtime:${dataType}:${timestamp}`
    ttl: 300 // 5 minutes
    data: RealtimeData
  }
}

// Cache Implementation
class CacheService {
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    const cached = await this.redis.get(key)
    if (cached) {
      return JSON.parse(cached)
    }
    
    const data = await fetcher()
    await this.redis.setex(key, ttl, JSON.stringify(data))
    return data
  }
  
  async invalidatePattern(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern)
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }
}
```

---

## 🚪 API Gateway i Routing

### Kong API Gateway Configuration

```yaml
# kong.yml
_format_version: "3.0"

services:
  - name: user-service
    url: http://user-service:3001
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
      - name: jwt
        config:
          secret_is_base64: false

  - name: supplement-service
    url: http://supplement-service:3002
    plugins:
      - name: rate-limiting
        config:
          minute: 200
          hour: 2000

routes:
  - name: user-routes
    service: user-service
    paths:
      - /api/v1/users
      - /api/v1/auth
      - /api/v1/profile

  - name: supplement-routes
    service: supplement-service
    paths:
      - /api/v1/supplements
      - /api/v1/search
      - /api/v1/interactions
```

### API Versioning Strategy

```typescript
// API Version Management
interface APIVersion {
  version: string
  deprecated: boolean
  sunsetDate?: Date
  migrationGuide?: string
}

const API_VERSIONS: APIVersion[] = [
  {
    version: 'v1',
    deprecated: false
  },
  {
    version: 'v2',
    deprecated: false
  }
]

// Route Handler with Versioning
app.use('/api/:version', (req, res, next) => {
  const version = req.params.version
  const versionInfo = API_VERSIONS.find(v => v.version === version)
  
  if (!versionInfo) {
    return res.status(404).json({
      error: 'API version not found',
      supportedVersions: API_VERSIONS.map(v => v.version)
    })
  }
  
  if (versionInfo.deprecated) {
    res.set('Sunset', versionInfo.sunsetDate?.toISOString())
    res.set('Deprecation', 'true')
  }
  
  next()
})
```

---

## ⚡ Caching Strategy

### Multi-Level Caching

```typescript
// L1: Application Cache (In-Memory)
class ApplicationCache {
  private cache = new Map<string, CacheEntry>()
  private maxSize = 1000
  
  set(key: string, value: any, ttl: number): void {
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }
    
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl * 1000,
      lastAccessed: Date.now()
    })
  }
  
  get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry || entry.expiry < Date.now()) {
      this.cache.delete(key)
      return null
    }
    
    entry.lastAccessed = Date.now()
    return entry.value
  }
}

// L2: Redis Cache (Distributed)
class DistributedCache {
  constructor(private redis: RedisClient) {}
  
  async get(key: string): Promise<any | null> {
    const value = await this.redis.get(key)
    return value ? JSON.parse(value) : null
  }
  
  async set(key: string, value: any, ttl: number): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value))
  }
}

// Cache-Aside Pattern
class CacheManager {
  constructor(
    private l1Cache: ApplicationCache,
    private l2Cache: DistributedCache,
    private database: Database
  ) {}
  
  async get<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    // L1 Cache check
    let data = this.l1Cache.get(key)
    if (data) return data
    
    // L2 Cache check
    data = await this.l2Cache.get(key)
    if (data) {
      this.l1Cache.set(key, data, 300) // 5 min L1 TTL
      return data
    }
    
    // Database fetch
    data = await fetcher()
    
    // Store in both caches
    this.l1Cache.set(key, data, 300)
    await this.l2Cache.set(key, data, 3600) // 1 hour L2 TTL
    
    return data
  }
}
```

---

## 🔒 Security Architecture

### Authentication & Authorization

```typescript
// JWT Token Structure
interface JWTPayload {
  sub: string // User ID
  email: string
  roles: string[]
  permissions: string[]
  iat: number
  exp: number
  iss: string
}

// Role-Based Access Control
enum Role {
  USER = 'user',
  PREMIUM_USER = 'premium_user',
  HEALTHCARE_PROVIDER = 'healthcare_provider',
  ADMIN = 'admin'
}

enum Permission {
  READ_PROFILE = 'read:profile',
  WRITE_PROFILE = 'write:profile',
  ACCESS_AI_ANALYSIS = 'access:ai_analysis',
  VIEW_RESEARCH = 'view:research',
  MANAGE_USERS = 'manage:users'
}

// Authorization Middleware
const authorize = (requiredPermissions: Permission[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user as JWTPayload
    
    const hasPermission = requiredPermissions.every(permission =>
      user.permissions.includes(permission)
    )
    
    if (!hasPermission) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        required: requiredPermissions
      })
    }
    
    next()
  }
}
```

### Data Encryption

```typescript
// Encryption Service
class EncryptionService {
  private algorithm = 'aes-256-gcm'
  private keyDerivation = 'pbkdf2'
  
  async encryptSensitiveData(data: any, userKey: string): Promise<EncryptedData> {
    const salt = crypto.randomBytes(16)
    const iv = crypto.randomBytes(16)
    
    const key = crypto.pbkdf2Sync(userKey, salt, 100000, 32, 'sha256')
    const cipher = crypto.createCipher(this.algorithm, key, iv)
    
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const authTag = cipher.getAuthTag()
    
    return {
      encrypted,
      salt: salt.toString('hex'),
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    }
  }
  
  async decryptSensitiveData(
    encryptedData: EncryptedData,
    userKey: string
  ): Promise<any> {
    const salt = Buffer.from(encryptedData.salt, 'hex')
    const iv = Buffer.from(encryptedData.iv, 'hex')
    const authTag = Buffer.from(encryptedData.authTag, 'hex')
    
    const key = crypto.pbkdf2Sync(userKey, salt, 100000, 32, 'sha256')
    const decipher = crypto.createDecipher(this.algorithm, key, iv)
    decipher.setAuthTag(authTag)
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return JSON.parse(decrypted)
  }
}
```

---

## 📊 Monitoring i Observability

### Metrics Collection

```typescript
// Prometheus Metrics
import { register, Counter, Histogram, Gauge } from 'prom-client'

const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
})

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route'],
  buckets: [0.1, 0.5, 1, 2, 5]
})

const activeUsers = new Gauge({
  name: 'active_users_total',
  help: 'Number of active users'
})

// Middleware for metrics collection
const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now()
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000
    
    httpRequestsTotal
      .labels(req.method, req.route?.path || req.path, res.statusCode.toString())
      .inc()
    
    httpRequestDuration
      .labels(req.method, req.route?.path || req.path)
      .observe(duration)
  })
  
  next()
}
```

### Distributed Tracing

```typescript
// OpenTelemetry Setup
import { NodeSDK } from '@opentelemetry/sdk-node'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'
import { JaegerExporter } from '@opentelemetry/exporter-jaeger'

const sdk = new NodeSDK({
  traceExporter: new JaegerExporter({
    endpoint: process.env.JAEGER_ENDPOINT
  }),
  instrumentations: [getNodeAutoInstrumentations()]
})

sdk.start()

// Custom Tracing
import { trace } from '@opentelemetry/api'

const tracer = trace.getTracer('supplement-service')

async function analyzeSupplementStack(supplements: Supplement[]) {
  const span = tracer.startSpan('analyze_supplement_stack')
  
  try {
    span.setAttributes({
      'supplement.count': supplements.length,
      'user.id': getCurrentUserId()
    })
    
    const result = await performAnalysis(supplements)
    
    span.setStatus({ code: SpanStatusCode.OK })
    return result
  } catch (error) {
    span.setStatus({
      code: SpanStatusCode.ERROR,
      message: error.message
    })
    throw error
  } finally {
    span.end()
  }
}
```

---

*Dokument aktualizowany: 2025-01-30*
