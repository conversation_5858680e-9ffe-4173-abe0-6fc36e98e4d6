# Infinite Knowledge Graph System

## Overview

The Infinite Knowledge Graph is a cutting-edge, real-time visualization system that automatically expands and learns from supplement and medical data. It combines AI processing, web crawling, and interactive visualization to create a living, breathing knowledge base.

## 🌟 Key Features

### Core Knowledge Graph Features
- **Dynamic Graph Structure**: Automatically expands as new supplement and medical information is added
- **Real-time Node Creation**: Creates nodes and relationships instantly when new data is crawled or processed
- **Intelligent Clustering**: Automatically organizes thousands of supplement nodes, ingredients, and interactions
- **Automatic Relationship Inference**: Discovers hidden connections between supplements, ingredients, and medical conditions

### Visual Interface
- **Interactive D3.js Visualization**: Smooth animations for node additions and updates
- **Dynamic Zoom & Pan**: Navigate large graph structures effortlessly
- **Visual Hierarchy**: Different node types (supplements, ingredients, conditions, studies) with distinct colors and shapes
- **Force-directed Layout**: Customizable physics for optimal node positioning
- **Progressive Loading**: Handles performance with large datasets efficiently

### Integration Points
- **Neo4j Backend**: Connected to robust graph database
- **CrawlService Integration**: Real-time updates from web crawling pipeline
- **Gemma3 AI Processing**: Shows confidence scores and relationship strengths
- **Export Capabilities**: Export graph sections for analysis

### Technical Implementation
- **React + TypeScript**: Type-safe, modern frontend architecture
- **WebSocket Connections**: Real-time graph updates
- **Caching Strategies**: Optimized performance with intelligent caching
- **Responsive Design**: Works seamlessly on different screen sizes

## 🏗️ Architecture

### Backend Components

#### GraphWebSocketService
```typescript
// Real-time WebSocket service for graph updates and interactions
// Manages WebSocket connections, client subscriptions, and broadcasts real-time graph updates.
// Orchestrates graph expansion, AI processing, and crawling operations.
class GraphWebSocketService {
  private io: SocketIOServer; // Socket.IO server instance
  private graphService: GraphService; // Backend GraphService for data operations
  private gemmaProcessor: Gemma3Neo4jProcessor; // AI processing for text-to-graph
  private crawlService: CrawlService; // Web crawling service
  private subscriptions: Map<string, ClientSubscription>; // Active client subscriptions
  private updateQueue: GraphUpdateEvent[]; // Queue for broadcasting updates
  
  constructor(server: HTTPServer);
  
  // Handles new client connections and sets up event listeners
  private setupEventHandlers(): void;
  
  // Sends initial graph data to a newly connected client based on filters
  private sendInitialGraphData(socket: any, filters: any): Promise<void>;
  
  // Expands the graph from a given node based on expansion type
  private expandGraphFromNode(nodeId: string, expansionType: string, limit: number): Promise<any>;
  
  // Finds similar nodes using AI embeddings (placeholder)
  private findSimilarNodes(node: any, limit: number): Promise<any>;
  
  // Infers relationships between nodes using various algorithms (placeholder)
  private inferRelationships(nodeIds: string[], algorithm: string): Promise<any>;
  
  // Performs advanced clustering on the graph (placeholder)
  private performAdvancedClustering(algorithm: string, threshold: number, options: any): Promise<any>;
  
  // Adds an update to the broadcast queue
  public broadcastUpdate(update: GraphUpdateEvent): void;
  
  // Processes updates from the queue and sends them to subscribed clients
  private processUpdateQueue(): Promise<void>;
  
  // Determines if an update should be sent to a specific client based on their filters
  private shouldSendUpdate(update: GraphUpdateEvent, subscription: ClientSubscription): boolean;
  
  // Starts a periodic processor for the update queue
  private startUpdateProcessor(): void;
  
  // Returns the number of currently connected clients
  public getConnectedClients(): number;
  
  // Returns the size of the update queue
  public getUpdateQueueSize(): number;
}
```

#### CrawlService
```typescript
// Web crawling and content extraction service
// Spawns a Python script (crawl4ai_wrapper.py) to perform crawling operations.
// Handles various crawling types and extracts supplement-specific data.
class CrawlService {
  private crawlScriptPath: string; // Path to the Python crawling script
  private tempDir: string; // Directory for temporary crawl files
  private activeCrawls: Map<string, ChildProcess>; // Map of active crawl processes
  
  constructor();
  
  // Crawls a single web page
  async crawlSinglePage(request: CrawlRequest): Promise<CrawlResult>;
  
  // Recursively crawls a website
  async crawlRecursive(request: CrawlRequest): Promise<CrawlResult[]>;
  
  // Crawls URLs from a sitemap
  async crawlSitemap(request: CrawlRequest): Promise<CrawlResult[]>;
  
  // Crawls content from an LLMs.txt or markdown file
  async crawlMarkdown(request: CrawlRequest): Promise<CrawlResult>;
  
  // Retrieves the progress of an active crawl
  async getCrawlProgress(crawlId: string): Promise<CrawlProgress>;
  
  // Cancels an active crawl process
  async cancelCrawl(crawlId: string): Promise<boolean>;
  
  // Executes a single crawl operation by spawning a Python process
  private executeCrawl(crawlId: string, request: CrawlRequest): Promise<CrawlResult>;
  
  // Executes a batch crawl operation for multiple pages
  private executeBatchCrawl(crawlId: string, request: CrawlRequest): Promise<CrawlResult[]>;
  
  // Processes raw crawl results and extracts supplement data
  private processCrawlResult(rawResult: any): CrawlResult;
  
  // Extracts supplement-specific keywords from content (can be enhanced with NLP)
  private extractSupplementData(content: string): ExtractedData;
  
  // Extracts keywords from text based on a provided list
  private extractKeywords(text: string, keywords: string[]): string[];
  
  // Extracts the domain from a given URL
  private extractDomain(url: string): string;
  
  // Generates a unique ID for a crawl operation
  private generateCrawlId(): string;
  
  // Ensures the temporary directory for crawl files exists
  private ensureTempDir(): Promise<void>;
  
  // Cleans up temporary crawl files
  private cleanupTempFiles(crawlId: string): Promise<void>;
  
  // Performs a health check for the crawl service (e.g., checks Python environment)
  async healthCheck(): Promise<boolean>;
}
```

### Frontend Components

#### InfiniteKnowledgeGraph
```typescript
// Main React component for the interactive Infinite Knowledge Graph visualization.
// Integrates D3.js for rendering, Socket.IO for real-time updates, and manages UI state.
const InfiniteKnowledgeGraph: React.FC<InfiniteKnowledgeGraphProps> = ({
  initialData, // Initial graph data to display
  width, // SVG width
  height, // SVG height
  autoExpansion, // Whether to enable automatic graph expansion
  expansionThreshold, // Threshold for auto-expansion importance
  maxNodes, // Maximum number of nodes to render
  onNodeSelect, // Callback for node selection
  onGraphUpdate, // Callback for graph data updates
  className // Additional CSS classes
}) => {
  // Refs for SVG, container, Socket.IO, D3 simulation, and zoom behavior
  const svgRef: React.RefObject<SVGSVGElement>;
  const containerRef: React.RefObject<HTMLDivElement>;
  const socketRef: React.MutableRefObject<Socket | null>;
  const simulationRef: React.MutableRefObject<d3.Simulation<GraphNode, GraphRelationship> | null>;
  const zoomRef: React.MutableRefObject<d3.ZoomBehavior<SVGSVGElement, unknown> | null>;
  const animationFrameRef: React.MutableRefObject<number | undefined>;

  // State variables for graph data, connection status, loading, selected/hovered nodes, etc.
  const [graphData, setGraphData]: [GraphData, React.Dispatch<React.SetStateAction<GraphData>>];
  const [filteredData, setFilteredData]: [GraphData, React.Dispatch<React.SetStateAction<GraphData>>];
  const [isConnected, setIsConnected]: [boolean, React.Dispatch<React.SetStateAction<boolean>>];
  const [isLoading, setIsLoading]: [boolean, React.Dispatch<React.SetStateAction<boolean>>];
  const [selectedNode, setSelectedNode]: [GraphNode | null, React.Dispatch<React.SetStateAction<GraphNode | null>>];
  const [hoveredNode, setHoveredNode]: [GraphNode | null, React.Dispatch<React.SetStateAction<GraphNode | null>>];
  const [clusters, setClusters]: [ClusterData[], React.Dispatch<React.SetStateAction<ClusterData[]>>];
  const [searchQuery, setSearchQuery]: [string, React.Dispatch<React.SetStateAction<string>>];
  const [isAutoExpanding, setIsAutoExpanding]: [boolean, React.Dispatch<React.SetStateAction<boolean>>];
  const [showClusters, setShowClusters]: [boolean, React.Dispatch<React.SetStateAction<boolean>>];
  const [showMetrics, setShowMetrics]: [boolean, React.Dispatch<React.SetStateAction<boolean>>];
  const [isFullscreen, setIsFullscreen]: [boolean, React.Dispatch<React.SetStateAction<boolean>>];

  // View state for visualization settings (zoom, pan, node size, etc.)
  const [viewState, setViewState]: [GraphViewState, React.Dispatch<React.SetStateAction<GraphViewState>>];

  // Filters for node types, relationship types, confidence, etc.
  const [filters, setFilters]: [any, React.Dispatch<React.SetStateAction<any>>];

  // Metrics for graph structure and performance
  const [graphMetrics, setGraphMetrics]: [GraphMetrics, React.Dispatch<React.SetStateAction<GraphMetrics>>];
  const [performanceMetrics, setPerformanceMetrics]: [PerformanceMetrics, React.Dispatch<React.SetStateAction<PerformanceMetrics>>];

  // Establishes WebSocket connection and handles incoming graph updates
  useEffect(() => { /* ... */ }, [filters, isAutoExpanding]);

  // Handles incoming graph update events from the WebSocket
  const handleGraphUpdate: (update: GraphUpdateEvent) => void;

  // Handles graph expansion events, merging new data and animating
  const handleGraphExpansion: (expansionData: any) => void;

  // Animates graph expansion with visual effects
  const animateGraphExpansion: (sourceNodeId: string, expansionType: string) => void;

  // Creates a sparkle effect at a given coordinate
  const createSparkleEffect: (x: number, y: number) => void;

  // Calculates various graph metrics (nodes, relationships, density, etc.)
  const calculateMetrics: (data: GraphData) => GraphMetrics;

  // Updates metrics when graph data changes
  useEffect(() => { /* ... */ }, [graphData, calculateMetrics, onGraphUpdate]);

  // Applies filters to the graph data
  useEffect(() => { /* ... */ }, [graphData, filters, searchQuery]);

  // Implements auto-expansion logic based on node importance
  useEffect(() => { /* ... */ }, [filteredData, isAutoExpanding, isConnected, expansionThreshold]);

  // Triggers manual graph expansion via WebSocket
  const expandGraph: (nodeId: string, expansionType?: string) => void;

  // Sends text to the backend for AI processing via WebSocket
  const processWithAI: (text: string) => void;

  // Initiates web crawling via WebSocket
  const startCrawl: (url: string, options?: any) => void;

  // Sets up and manages the D3.js force-directed graph visualization
  useEffect(() => { /* ... */ }, [filteredData, viewState, width, height, isAutoExpanding, onNodeSelect]);

  // Helper function to determine node color based on type
  const getNodeColor: (node: GraphNode) => string;

  // Helper function to determine node size based on importance and connections
  const getNodeSize: (node: GraphNode) => number;

  // Helper function to determine relationship color based on type
  const getRelationshipColor: (type: string) => string;

  // Helper function to determine relationship width based on strength
  const getRelationshipWidth: (strength?: string) => number;

  // Highlights connected nodes when a node is hovered
  const highlightConnectedNodes: (nodeId: string) => void;

  // Clears node highlights
  const clearHighlights: () => void;

  // Placeholder for showing tooltips on nodes/relationships
  const showTooltip: (event: any, data: any, type: 'node' | 'relationship') => void;

  // Placeholder for hiding tooltips
  const hideTooltip: () => void;

  // JSX for rendering the component UI
  return ( /* ... */ );
};
```

#### GraphService
```typescript
// Frontend service for managing graph data, caching, and performance.
// Interacts with the backend API to load and expand graph data.
class GraphService {
  private cache: GraphCache; // In-memory cache for nodes, relationships, queries, and expansions
  private loadingState: LoadingState; // Current loading status and progress
  private performanceConfig: PerformanceConfig; // Configuration for performance parameters
  private eventListeners: Map<string, Function[]>; // Custom event system listeners
  private updateQueue: GraphUpdateEvent[]; // Queue for processing graph updates
  private isProcessingUpdates: boolean; // Flag to prevent concurrent update processing
  private performanceMetrics: PerformanceMetrics; // Real-time performance metrics
  private abortController: AbortController; // Controller for aborting fetch requests
  
  constructor(config?: Partial<PerformanceConfig>);
  
  // Sets an entry in the cache with a given key, data, and optional TTL
  private setCacheEntry<T>(cache: Map<string, CacheEntry<T>>, key: string, data: T, ttl?: number): void;
  
  // Retrieves an entry from the cache, returning null if expired or not found
  private getCacheEntry<T>(cache: Map<string, CacheEntry<T>>, key: string): T | null;
  
  // Cleans up the cache by removing expired and least recently used entries
  private cleanupCache<T>(cache: Map<string, CacheEntry<T>>): void;
  
  // Loads graph data from the backend API, utilizing caching
  public async loadGraphData(options?: {
    nodeTypes?: string[];
    limit?: number;
    strategy?: LoadingStrategy;
  }): Promise<GraphData>;
  
  // Expands the graph from a specific node, fetching related data from the backend
  public async expandGraph(nodeId: string, expansionType: string, options?: {
    limit?: number;
    depth?: number;
    filters?: any;
  }): Promise<GraphData>;
  
  // Loads nodes that are currently visible in the viewport (placeholder for implementation)
  public async loadNodesInViewport(viewport: {
    x: number;
    y: number;
    width: number;
    height: number;
    zoom: number;
  }): Promise<GraphNode[]>;
  
  // Placeholder for calculating visible nodes in the viewport
  private async getNodesInViewport(viewport: any): Promise<string[]>;
  
  // Updates the loading progress based on loaded and pending nodes
  private updateLoadingProgress(): void;
  
  // Adds a graph update event to the processing queue
  public queueUpdate(update: GraphUpdateEvent): void;
  
  // Processes updates from the queue in batches
  private async processUpdateQueue(): Promise<void>;
  
  // Processes a single graph update event, updating the cache
  private async processUpdate(update: GraphUpdateEvent): Promise<void>;
  
  // Starts a periodic performance monitoring loop
  private startPerformanceMonitoring(): void;
  
  // Updates performance metrics based on current graph data
  private updatePerformanceMetrics(data: GraphData): void;
  
  // Starts a periodic cache cleanup process
  private startCacheCleanup(): void;
  
  // Registers an event listener for a custom event
  public on(event: string, callback: Function): void;
  
  // Unregisters an event listener
  public off(event: string, callback: Function): void;
  
  // Emits a custom event with optional data
  private emit(event: string, data?: any): void;
  
  // Returns the current loading state
  public getLoadingState(): LoadingState;
  
  // Returns the current performance metrics
  public getPerformanceMetrics(): PerformanceMetrics;
  
  // Returns statistics about the cache size
  public getCacheStats(): {
    nodes: number;
    relationships: number;
    queries: number;
    expansions: number;
  };
  
  // Clears all cached data
  public clearCache(): void;
  
  // Cleans up resources when the service is destroyed
  public destroy(): void;
}
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Neo4j database
- Redis cache
- MongoDB
- Weaviate (optional)

### Installation

1. **Backend Setup**
```bash
cd backend
npm install
# Ensure Neo4j, Redis, and MongoDB are running as per your environment setup.
# For local development, you might use docker-compose up -d in the project root.
npx tsx src/index.ts
```

2. **Frontend Setup**
```bash
cd frontend
npm install
npm run dev
```

3. **Access the Demo**
Navigate to: `http://localhost:5174/infinite-graph`

## 🎮 Usage Guide

### Basic Interaction
1. **Node Selection**: Click any node to select and view details
2. **Graph Expansion**: Double-click nodes to expand related connections
3. **Auto-Expansion**: Toggle automatic expansion based on importance
4. **Search**: Use the search bar to find specific nodes
5. **Filtering**: Apply filters by node type, confidence, or relationship type

### Advanced Features
1. **AI Processing**: Input text to generate new nodes and relationships
2. **Web Crawling**: Crawl websites to extract supplement information
3. **Real-time Updates**: Watch the graph grow in real-time
4. **Performance Monitoring**: Track rendering performance and memory usage

### Demo Controls
- **Simulate AI Processing**: Adds AI-generated nodes and relationships
- **Simulate Web Crawling**: Adds nodes from crawled content
- **Expand Selected**: Manually expand from selected node
- **Reset View**: Return to default zoom and position

## 🔧 Configuration

### Graph Settings
```typescript
interface GraphViewState {
  nodeSize: number;           // Base node size (4-20)
  linkDistance: number;       // Distance between connected nodes (20-200)
  chargeStrength: number;     // Force simulation strength (-500 to -50)
  showLabels: boolean;        // Display node labels
  hoverEffects: boolean;      // Enable hover animations
  particleEffects: boolean;   // Enable particle effects
  clustering: {
    enabled: boolean;         // Enable automatic clustering
    threshold: number;        // Clustering sensitivity (0-1)
    algorithm: string;        // Clustering algorithm
  };
}
```

### Performance Settings
```typescript
interface PerformanceConfig {
  maxNodes: number;           // Maximum nodes to render (default: 5000)
  maxRelationships: number;   // Maximum relationships (default: 10000)
  renderThreshold: number;    // Rendering optimization threshold
  updateBatchSize: number;    // Batch size for updates
  cacheSize: number;          // Cache size limit
  cacheTTL: number;           // Cache time-to-live
}
```

## 📊 Performance Metrics

The system tracks comprehensive performance metrics:

- **Rendering Performance**: FPS, render time, update time
- **Memory Usage**: Node count, relationship count, cache usage
- **Network Performance**: WebSocket connection status, update frequency
- **User Interaction**: Expansion count, AI processing requests, crawl operations

## 🎨 Customization

### Visual Themes
The graph supports multiple visual themes:
- **Default**: Professional blue and green palette
- **Vibrant**: High-contrast, colorful theme
- **Professional**: Muted, business-appropriate colors

### Node Types and Colors
- **SUPPLEMENT**: Blue (#6366f1)
- **INGREDIENT**: Green (#10b981)
- **CONDITION**: Amber (#f59e0b)
- **MECHANISM**: Purple (#8b5cf6)
- **DOSAGE**: Cyan (#06b6d4)
- **STUDY**: Lime (#84cc16)
- **EFFECT**: Red (#ef4444)
- **POPULATION**: Orange (#f97316)

### Relationship Types
- **TREATS**: Therapeutic relationships
- **PREVENTS**: Preventive effects
- **ENHANCES**: Synergistic interactions
- **INHIBITS**: Inhibitory effects
- **SYNERGISTIC_WITH**: Positive combinations
- **CONTRAINDICATED_WITH**: Dangerous combinations
- **ACTS_VIA**: Mechanism of action
- **AFFECTS_ABSORPTION**: Absorption interactions

## 🔮 Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Advanced relationship prediction
2. **Collaborative Editing**: Multi-user graph editing
3. **3D Visualization**: Three-dimensional graph representation
4. **Voice Commands**: Voice-controlled graph navigation
5. **AR/VR Support**: Immersive graph exploration
6. **Advanced Analytics**: Graph analysis and insights
7. **API Integration**: Connect to external medical databases
8. **Mobile App**: Native mobile graph exploration

### Performance Optimizations
1. **WebGL Rendering**: Hardware-accelerated graphics
2. **Virtual Scrolling**: Handle millions of nodes
3. **Incremental Updates**: Minimal re-rendering
4. **Background Processing**: Web Workers for heavy computations
5. **Edge Computing**: Distributed graph processing

## 🤝 Contributing

We welcome contributions!

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file in the project root for details.

## 🙏 Acknowledgments

- **D3.js**: Powerful data visualization library
- **Neo4j**: Graph database excellence
- **React**: Modern UI framework
- **Socket.IO**: Real-time communication
- **Gemma3**: AI processing capabilities
- **The Open Source Community**: For inspiration and tools

---

**Built with ❤️ for the future of supplement knowledge discovery**
